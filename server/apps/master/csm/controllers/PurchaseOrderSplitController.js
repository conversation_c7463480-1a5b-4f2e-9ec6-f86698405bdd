/**
 * 采购订单拆分控制器
 * 负责处理采购订单商品拆分相关的请求和响应
 */
const BaseController = require('../../../../core/controllers/BaseController');

class PurchaseOrderSplitController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
  }

  /**
   * 获取采购订单拆分列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getPurchaseOrderSplits(req, res) {
    try {
      const { orderId } = req.params;
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;
      
      if (!orderId) {
        return this.fail(res, '采购订单ID不能为空', 400);
      }

      // 查询拆分列表
      const where = {
        original_purchase_order_id: BigInt(orderId),
        deleted_at: null
      };

      const [splits, total] = await Promise.all([
        this.prisma.purchase_order_split.findMany({
          where,
          include: {
            split_items: {
              where: { deleted_at: null }
            }
          },
          orderBy: { created_at: 'desc' },
          skip: (page - 1) * pageSize,
          take: pageSize
        }),
        this.prisma.purchase_order_split.count({ where })
      ]);

      // 格式化返回数据为驼峰形式
      const formattedSplits = splits.map(split => ({
        id: split.id.toString(),
        splitNumber: split.split_number,
        originalPurchaseOrderId: split.original_purchase_order_id.toString(),
        originalOrderNumber: split.original_order_number,
        splitType: split.split_type,
        recipientName: split.recipient_name,
        recipientPhone: split.recipient_phone,
        deliveryAddress: split.delivery_address,
        orderSource: split.order_source,
        splitStatus: split.split_status,
        totalSplitQuantity: split.total_split_quantity,
        splitterId: split.splitter_id?.toString(),
        splitterName: split.splitter_name,
        splitTime: split.split_time,
        submitter: split.submitter,
        updater: split.updater,
        createdAt: split.created_at,
        updatedAt: split.updated_at,
        deletedAt: split.deleted_at,
        splitItems: split.split_items.map(item => ({
          id: item.id.toString(),
          splitId: item.split_id.toString(),
          originalItemId: item.original_item_id.toString(),
          goodsSpuId: item.goods_spu_id?.toString(),
          goodsSkuId: item.goods_sku_id.toString(),
          productName: item.product_name,
          productCode: item.product_code,
          skuCode: item.sku_code,
          sku: item.sku,
          specification: item.specification,
          productImage: item.product_image,
          unitPrice: item.unit_price,
          splitQuantity: item.split_quantity,
          shippingStatus: item.shipping_status,
          shippingNumber: item.shipping_number,
          shippingInfo: item.shipping_info,
          createdAt: item.created_at,
          updatedAt: item.updated_at,
          deletedAt: item.deleted_at
        }))
      }));

      return this.success(res, {
        items: formattedSplits,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }, '获取拆分列表成功');

    } catch (error) {
      console.error('获取采购订单拆分列表失败', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取采购订单拆分详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getPurchaseOrderSplitDetail(req, res) {
    try {
      const { splitId } = req.params;
      
      if (!splitId) {
        return this.fail(res, '拆分单ID不能为空', 400);
      }

      const split = await this.prisma.purchase_order_split.findFirst({
        where: {
          id: BigInt(splitId),
          deleted_at: null
        },
        include: {
          original_purchase_order: {
            include: {
              purchase_order_items: {
                where: { deleted_at: null }
              }
            }
          },
          split_items: {
            where: { deleted_at: null },
            orderBy: { created_at: 'asc' }
          }
        }
      });

      if (!split) {
        return this.fail(res, '拆分单不存在', 404);
      }

      // 格式化返回数据为驼峰形式
      const formattedSplit = {
        id: split.id.toString(),
        splitNumber: split.split_number,
        originalPurchaseOrderId: split.original_purchase_order_id.toString(),
        originalOrderNumber: split.original_order_number,
        splitType: split.split_type,
        recipientName: split.recipient_name,
        recipientPhone: split.recipient_phone,
        deliveryAddress: split.delivery_address,
        orderSource: split.order_source,
        splitStatus: split.split_status,
        totalSplitQuantity: split.total_split_quantity,
        splitterId: split.splitter_id?.toString(),
        splitterName: split.splitter_name,
        splitTime: split.split_time,
        submitter: split.submitter,
        updater: split.updater,
        createdAt: split.created_at,
        updatedAt: split.updated_at,
        deletedAt: split.deleted_at,
        originalPurchaseOrder: {
          id: split.original_purchase_order.id.toString(),
          purchaseOrderNumber: split.original_purchase_order.purchase_order_number,
          originalOrderId: split.original_purchase_order.original_order_id?.toString(),
          originalOrderNumber: split.original_purchase_order.original_order_number,
          thirdPartyOrderSn: split.original_purchase_order.third_party_order_sn,
          purchaserId: split.original_purchase_order.purchaser_id?.toString(),
          purchaserName: split.original_purchase_order.purchaser_name,
          supplierId: split.original_purchase_order.supplier_id?.toString(),
          supplierName: split.original_purchase_order.supplier_name,
          supplierCode: split.original_purchase_order.supplier_code,
          orderSource: split.original_purchase_order.order_source,
          orderType: split.original_purchase_order.order_type,
          follower: split.original_purchase_order.follower,
          buyerAccount: split.original_purchase_order.buyer_account,
          deliveryAddress: split.original_purchase_order.delivery_address,
          recipientName: split.original_purchase_order.recipient_name,
          recipientPhone: split.original_purchase_order.recipient_phone,
          recipientAddress: split.original_purchase_order.recipient_address,
          actualReceiver: split.original_purchase_order.actual_receiver,
          actualPhone: split.original_purchase_order.actual_phone,
          actualAddress: split.original_purchase_order.actual_address,
          totalQuantity: split.original_purchase_order.total_quantity,
          totalAmount: split.original_purchase_order.total_amount,
          createdAt: split.original_purchase_order.created_at,
          updatedAt: split.original_purchase_order.updated_at,
          purchaseOrderItems: split.original_purchase_order.purchase_order_items.map(item => ({
            id: item.id.toString(),
            purchaseOrderId: item.purchase_order_id.toString(),
            originalOrderItemId: item.original_order_item_id?.toString(),
            goodsSpuId: item.goods_spu_id?.toString(),
            goodsSkuId: item.goods_sku_id.toString(),
            spuCodeSnapshot: item.spu_code_snapshot,
            spuNameSnapshot: item.spu_name_snapshot,
            productName: item.product_name,
            productCode: item.product_code,
            skuCode: item.sku_code,
            sku: item.sku,
            specification: item.specification,
            productImage: item.product_image,
            unitPrice: item.unit_price,
            quantity: item.quantity,
            splitQuantity: item.split_quantity,
            availableSplitQuantity: item.available_split_quantity,
            purchasedQuantity: item.purchased_quantity,
            receivedQuantity: item.received_quantity,
            totalPrice: item.total_price,
            totalAmount: item.total_amount,
            itemStatus: item.item_status,
            createdAt: item.created_at,
            updatedAt: item.updated_at
          }))
        },
        splitItems: split.split_items.map(item => ({
          id: item.id.toString(),
          splitId: item.split_id.toString(),
          originalItemId: item.original_item_id.toString(),
          goodsSpuId: item.goods_spu_id?.toString(),
          goodsSkuId: item.goods_sku_id.toString(),
          productName: item.product_name,
          productCode: item.product_code,
          skuCode: item.sku_code,
          sku: item.sku,
          specification: item.specification,
          productImage: item.product_image,
          unitPrice: item.unit_price,
          splitQuantity: item.split_quantity,
          shippingStatus: item.shipping_status,
          shippingNumber: item.shipping_number,
          shippingInfo: item.shipping_info,
          createdAt: item.created_at,
          updatedAt: item.updated_at,
          deletedAt: item.deleted_at
        }))
      };

      return this.success(res, formattedSplit, '获取拆分详情成功');

    } catch (error) {
      console.error('获取采购订单拆分详情失败', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 提交采购订单拆分
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async submitPurchaseOrderSplit(req, res) {
    try {
      const { orderId } = req.params;
      const { splitData } = req.body;
      const currentUser = req.user || { id: 1, nickname: '系统用户', username: 'system' };

      if (!orderId) {
        return this.fail(res, '采购订单ID不能为空', 400);
      }

      if (!splitData) {
        return this.fail(res, '拆分数据不能为空', 400);
      }

      // 验证采购订单是否存在
      const purchaseOrder = await this.prisma.purchase_order.findFirst({
        where: {
          id: BigInt(orderId),
          deleted_at: null
        },
        include: {
          purchase_order_items: {
            where: { deleted_at: null }
          }
        }
      });

      if (!purchaseOrder) {
        return this.fail(res, '采购订单不存在', 404);
      }

      // 处理拆分数据
      const { splitResults, splitGroups } = splitData;

      if (!splitResults || splitResults.length === 0) {
        return this.fail(res, '没有拆分数据', 400);
      }

      // 生成拆分单编号：原订单号-编号（从1开始）
      const existingSplitsCount = await this.prisma.purchase_order_split.count({
        where: {
          original_purchase_order_id: BigInt(orderId),
          deleted_at: null
        }
      });
      const splitNumber = `${purchaseOrder.purchase_order_number}-${existingSplitsCount + 1}`;

      // 开始事务处理
      const result = await this.prisma.$transaction(async (tx) => {
        // 创建拆分单（精简版）
        const split = await tx.purchase_order_split.create({
          data: {
            split_number: splitNumber,
            original_purchase_order_id: BigInt(orderId),
            original_order_number: purchaseOrder.purchase_order_number,
            split_type: splitData.type === 'single-product-split' ? 1 : 2,
            recipient_name: purchaseOrder.recipient_name,
            recipient_phone: purchaseOrder.recipient_phone,
            delivery_address: purchaseOrder.delivery_address,
            order_source: purchaseOrder.order_source,
            split_status: 2, // 已完成
            total_split_quantity: splitResults.reduce((sum, r) => sum + r.totalAllocated, 0),
            splitter_id: BigInt(currentUser.id),
            splitter_name: currentUser.nickname || currentUser.username,
            submitter: currentUser.username,
            updater: currentUser.username
          }
        });

        // 创建拆分商品项并更新原商品项
        for (const splitResult of splitResults) {
          const { originalProduct, splitItems } = splitResult;

          // 查找原始商品项
          const originalItem = purchaseOrder.purchase_order_items.find(
            item => item.id.toString() === originalProduct.id.toString()
          );

          if (!originalItem) {
            throw new Error(`商品项${originalProduct.id}不存在`);
          }

          // 创建拆分商品项
          for (const splitItem of splitItems) {
            await tx.purchase_order_split_item.create({
              data: {
                split_id: split.id,
                original_item_id: BigInt(originalProduct.id),
                goods_spu_id: originalItem.goods_spu_id,
                goods_sku_id: originalItem.goods_sku_id,
                product_name: originalItem.product_name,
                product_code: originalItem.product_code,
                sku_code: originalItem.sku_code,
                sku: originalItem.sku,
                specification: originalItem.specification,
                product_image: originalItem.product_image,
                unit_price: originalItem.unit_price,
                split_quantity: splitItem.quantity,
                shipping_status: 0
              }
            });
          }

          // 更新原商品项的拆分数量
          const totalSplitQty = splitItems.reduce((sum, item) => sum + item.quantity, 0);
          await tx.purchase_order_item.update({
            where: { id: BigInt(originalProduct.id) },
            data: {
              split_quantity: originalItem.split_quantity + totalSplitQty,
              available_split_quantity: originalItem.available_split_quantity - totalSplitQty,
              updater: currentUser.username
            }
          });
        }

        // 更新采购订单的拆分状态
        const orderTotalSplit = splitResults.reduce((sum, r) => sum + r.totalAllocated, 0);
        const orderTotalRemaining = splitResults.reduce((sum, r) => sum + r.remainingQuantity, 0);

        let orderSplitStatus = 0; // 未拆分
        if (orderTotalSplit > 0 && orderTotalRemaining > 0) {
          orderSplitStatus = 1; // 部分拆分
        } else if (orderTotalSplit > 0 && orderTotalRemaining === 0) {
          orderSplitStatus = 2; // 全部拆分
        }

        await tx.purchase_order.update({
          where: { id: BigInt(orderId) },
          data: {
            split_status: orderSplitStatus,
            split_count: purchaseOrder.split_count + 1,
            updater: currentUser.username
          }
        });

        return split;
      });

      return this.success(res, {
        id: result.id.toString(),
        splitNumber: result.split_number,
        splitStatus: result.split_status,
        totalSplitQuantity: result.total_split_quantity
      }, '拆分提交成功');

    } catch (error) {
      console.error('提交采购订单拆分失败', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取采购订单商品信息（包含可拆分数量）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getPurchaseOrderItems(req, res) {
    try {
      const { orderId } = req.params;

      if (!orderId) {
        return this.fail(res, '采购订单ID不能为空', 400);
      }

      // 查询采购订单及其商品项，只选择拆分所需的字段
      const purchaseOrder = await this.prisma.purchase_order.findFirst({
        where: {
          id: BigInt(orderId),
          deleted_at: null
        },
        select: {
          id: true,
          purchase_order_number: true,
          order_source: true,
          recipient_name: true,
          recipient_phone: true,
          recipient_address: true,
          total_quantity: true,
          purchase_order_items: {
            where: { deleted_at: null },
            select: {
              id: true,
              product_name: true,
              product_code: true,
              sku: true,
              specification: true,
              product_image: true,
              unit_price: true,
              quantity: true
            },
            orderBy: { created_at: 'asc' }
          }
        }
      });

      if (!purchaseOrder) {
        return this.fail(res, '采购订单不存在', 404);
      }

      // 计算每个商品的可拆分数量
      const itemsWithSplitInfo = await Promise.all(
        purchaseOrder.purchase_order_items.map(async (item) => {
          // 查询该商品已拆分的数量
          const splitQuantity = await this.prisma.purchase_order_split_item.aggregate({
            where: {
              original_item_id: item.id,
              deleted_at: null
            },
            _sum: {
              split_quantity: true
            }
          });

          const totalSplitQuantity = splitQuantity._sum.split_quantity || 0;
          const availableSplitQuantity = Math.max(0, item.quantity - totalSplitQuantity);

          return {
            ...item,
            split_quantity: totalSplitQuantity,
            available_split_quantity: availableSplitQuantity
          };
        })
      );

      // 格式化返回数据为驼峰形式，只返回拆分所需的字段
      const formattedOrder = {
        id: purchaseOrder.id.toString(),
        orderNumber: purchaseOrder.purchase_order_number,
        orderSource: purchaseOrder.order_source,
        recipientName: purchaseOrder.recipient_name,
        recipientPhone: purchaseOrder.recipient_phone,
        recipientAddress: purchaseOrder.recipient_address,
        totalQuantity: purchaseOrder.total_quantity,
        items: itemsWithSplitInfo.map(item => ({
          id: item.id.toString(),
          productName: item.product_name,
          productCode: item.product_code,
          sku: item.sku,
          specification: item.specification,
          productImage: item.product_image,
          unitPrice: item.unit_price,
          quantity: item.quantity,
          splitQuantity: item.split_quantity,
          availableSplitQuantity: item.available_split_quantity,
          // 拆分相关的计算字段
          canSplit: item.available_split_quantity > 0,
          splitProgress: item.quantity > 0 ? ((item.split_quantity / item.quantity) * 100).toFixed(2) : '0.00'
        }))
      };

      // 计算订单级别的拆分统计
      const orderStats = {
        totalItems: formattedOrder.items.length,
        totalQuantity: formattedOrder.items.reduce((sum, item) => sum + item.quantity, 0),
        totalSplitQuantity: formattedOrder.items.reduce((sum, item) => sum + item.splitQuantity, 0),
        totalAvailableSplitQuantity: formattedOrder.items.reduce((sum, item) => sum + item.availableSplitQuantity, 0),
        splittableItemsCount: formattedOrder.items.filter(item => item.canSplit).length,
        splitProgress: formattedOrder.totalQuantity > 0 ?
          ((formattedOrder.items.reduce((sum, item) => sum + item.splitQuantity, 0) / formattedOrder.totalQuantity) * 100).toFixed(2) : '0.00'
      };

      return this.success(res, {
        order: formattedOrder,
        stats: orderStats
      }, '获取订单商品信息成功');

    } catch (error) {
      console.error('获取采购订单商品信息失败', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 拆分商品接口
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async splitProducts(req, res) {
    try {
      const { orderId } = req.params;
      const { splitData } = req.body;
      const currentUser = req.user || { id: 1, nickname: '系统用户', username: 'system' };

      if (!orderId) {
        return this.fail(res, '采购订单ID不能为空', 400);
      }

      if (!splitData || !splitData.splitResults || splitData.splitResults.length === 0) {
        return this.fail(res, '拆分数据不能为空', 400);
      }

      // 验证采购订单是否存在
      const purchaseOrder = await this.prisma.purchase_order.findFirst({
        where: {
          id: BigInt(orderId),
          deleted_at: null
        },
        include: {
          purchase_order_items: {
            where: { deleted_at: null }
          }
        }
      });

      if (!purchaseOrder) {
        return this.fail(res, '采购订单不存在', 404);
      }

      // 验证拆分数据
      const { splitResults } = splitData;
      for (const splitResult of splitResults) {
        const { originalProduct, splitItems } = splitResult;

        // 查找原始商品项
        const originalItem = purchaseOrder.purchase_order_items.find(
          item => item.id.toString() === originalProduct.id.toString()
        );

        if (!originalItem) {
          return this.fail(res, `商品项${originalProduct.id}不存在`, 400);
        }

        // 验证拆分数量
        const totalSplitQty = splitItems.reduce((sum, item) => sum + (item.quantity || 0), 0);
        if (totalSplitQty > originalItem.available_split_quantity) {
          return this.fail(res, `商品"${originalItem.product_name}"的拆分数量超过可分配数量`, 400);
        }

        if (totalSplitQty <= 0) {
          return this.fail(res, `商品"${originalItem.product_name}"的拆分数量必须大于0`, 400);
        }
      }

      // 生成拆分单编号：原订单号-编号（从1开始）
      const existingSplitsCount = await this.prisma.purchase_order_split.count({
        where: {
          original_purchase_order_id: BigInt(orderId),
          deleted_at: null
        }
      });
      const splitNumber = `${purchaseOrder.purchase_order_number}-${existingSplitsCount + 1}`;

      // 开始事务处理
      const result = await this.prisma.$transaction(async (tx) => {
        // 创建拆分单
        const split = await tx.purchase_order_split.create({
          data: {
            split_number: splitNumber,
            original_purchase_order_id: BigInt(orderId),
            original_order_number: purchaseOrder.purchase_order_number,
            split_type: splitResults.length === 1 ? 1 : 2, // 1-单商品拆分 2-多商品拆分
            recipient_name: purchaseOrder.recipient_name,
            recipient_phone: purchaseOrder.recipient_phone,
            delivery_address: purchaseOrder.delivery_address,
            order_source: purchaseOrder.order_source,
            split_status: 2, // 已完成
            total_split_quantity: splitResults.reduce((sum, r) => sum + r.splitItems.reduce((s, i) => s + i.quantity, 0), 0),
            splitter_id: BigInt(currentUser.id),
            splitter_name: currentUser.nickname || currentUser.username,
            submitter: currentUser.username,
            updater: currentUser.username
          }
        });

        // 创建拆分商品项并更新原商品项
        for (const splitResult of splitResults) {
          const { originalProduct, splitItems } = splitResult;

          // 查找原始商品项
          const originalItem = purchaseOrder.purchase_order_items.find(
            item => item.id.toString() === originalProduct.id.toString()
          );

          // 创建拆分商品项
          for (const splitItem of splitItems) {
            await tx.purchase_order_split_item.create({
              data: {
                split_id: split.id,
                original_item_id: BigInt(originalProduct.id),
                goods_spu_id: originalItem.goods_spu_id,
                goods_sku_id: originalItem.goods_sku_id,
                product_name: originalItem.product_name,
                product_code: originalItem.product_code,
                sku_code: originalItem.sku_code,
                sku: originalItem.sku,
                specification: originalItem.specification,
                product_image: originalItem.product_image,
                unit_price: originalItem.unit_price,
                split_quantity: splitItem.quantity,
                shipping_status: 0
              }
            });
          }

          // 更新原商品项的拆分数量
          const totalSplitQty = splitItems.reduce((sum, item) => sum + item.quantity, 0);
          await tx.purchase_order_item.update({
            where: { id: BigInt(originalProduct.id) },
            data: {
              split_quantity: originalItem.split_quantity + totalSplitQty,
              available_split_quantity: originalItem.available_split_quantity - totalSplitQty,
              updated_at: new Date()
            }
          });
        }

        // 更新采购订单的拆分状态
        const orderTotalSplit = splitResults.reduce((sum, r) => sum + r.splitItems.reduce((s, i) => s + i.quantity, 0), 0);
        const orderTotalRemaining = splitResults.reduce((sum, r) => {
          const originalItem = purchaseOrder.purchase_order_items.find(
            item => item.id.toString() === r.originalProduct.id.toString()
          );
          const totalSplitQty = r.splitItems.reduce((s, i) => s + i.quantity, 0);
          return sum + (originalItem.available_split_quantity - totalSplitQty);
        }, 0);

        let orderSplitStatus = 0; // 未拆分
        if (orderTotalSplit > 0 && orderTotalRemaining > 0) {
          orderSplitStatus = 1; // 部分拆分
        } else if (orderTotalSplit > 0 && orderTotalRemaining === 0) {
          orderSplitStatus = 2; // 全部拆分
        }

        await tx.purchase_order.update({
          where: { id: BigInt(orderId) },
          data: {
            split_status: orderSplitStatus,
            split_count: purchaseOrder.split_count + 1,
            updated_at: new Date()
          }
        });

        return split;
      });

      return this.success(res, {
        id: result.id.toString(),
        splitNumber: result.split_number,
        splitStatus: result.split_status,
        totalSplitQuantity: result.total_split_quantity
      }, '商品拆分成功');

    } catch (error) {
      console.error('拆分商品失败', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }
}

module.exports = PurchaseOrderSplitController;
