/**
 * 售后申请服务层
 * 负责处理售后申请相关的业务逻辑
 */
const { PrismaClient } = require('@prisma/client');
const PurchaseOrderService = require('./PurchaseOrderService');

class AfterSalesService {
  constructor(prisma) {
    this.prisma = prisma || new PrismaClient();
    this.purchaseOrderService = new PurchaseOrderService(this.prisma);
  }

  /**
   * 生成售后编号
   * @returns {string} - 售后编号
   */
  generateAfterSalesNumber() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');

    // 使用完整的时间戳 + 随机数确保唯一性
    const timestamp = now.getTime().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const uniqueId = (timestamp + random).slice(-9); // 取后9位确保长度合理

    return `AS${year}${month}${day}${uniqueId}`;
  }

  /**
   * 创建售后申请
   * @param {Object} applicationData - 售后申请数据
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 创建结果
   */
  async createAfterSalesApplication(applicationData, currentUser) {
    try {
      const {
        orderNumber,
        afterSalesType,
        afterSalesContent,
        customerDemand,
        purchaser,
        purchaser_id,
        afterSalesStaff,
        after_sales_staff_id,
        products = [],
        attachments = []
      } = applicationData;

      // 1. 直接根据订单号查询采购订单详情
      const purchaseOrder = await this.prisma.purchase_order.findFirst({
        where: {
          original_order_number: orderNumber,
          deleted_at: null
        }
      });

      if (!purchaseOrder) {
        throw new Error(`订单号 ${orderNumber} 对应的采购订单不存在`);
      }

      console.log('purchaseOrder', purchaseOrder);

      // 2. 检查是否已经申请过售后
      const existingApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          original_order_number: orderNumber,
          deleted_at: null
        }
      });


      // 3. 生成售后编号
      const afterSalesNumber = this.generateAfterSalesNumber();

      // 4. 获取采购订单商品信息（用于创建售后商品项）
      const purchaseOrderInfo = await this.purchaseOrderService.getPurchaserByOrderNumber(orderNumber);

      // 5. 开始事务创建售后申请 - 根据products数组为每个商品创建一条售后记录
      const results = await this.prisma.$transaction(async (tx) => {
        const createdApplications = [];

        // 遍历products数组，为每个商品创建一条售后申请记录
        for (let i = 0; i < products.length; i++) {
          const product = products[i];

          // 为每个商品生成独立的售后编号（每个都是完全不同的编号）
          const productAfterSalesNumber = this.generateAfterSalesNumber();

          // 创建售后申请主记录
          const afterSalesApplication = await tx.afterSalesApplication.create({
            data: {
              after_sales_number: productAfterSalesNumber,

              // 订单关联信息
              original_order_id: purchaseOrder?.original_order_id ? BigInt(purchaseOrder.original_order_id) : null,
              original_order_number: orderNumber,
              purchase_order_id: purchaseOrder ? purchaseOrder.id : null,
              purchase_order_number: purchaseOrder ? purchaseOrder.purchase_order_number : null,

              // 售后基础信息
              after_sales_type: afterSalesType,
              after_sales_content: afterSalesContent,
              customer_demand: customerDemand,

              // 人员信息
              applicant_id: BigInt(currentUser.id),
              applicant_name: currentUser.nickname || currentUser.username,
              purchaser_id: purchaser_id ? BigInt(purchaser_id) :
                (purchaseOrder?.purchaser_id ? BigInt(purchaseOrder.purchaser_id) : null),
              purchaser_name: purchaser || purchaseOrder?.purchaser_name,
              after_sales_staff_id: after_sales_staff_id ? BigInt(after_sales_staff_id) : null,
              after_sales_staff_name: afterSalesStaff,

              // 从采购订单同步的基础信息
              order_source: purchaseOrder?.order_source || null,
              order_type: purchaseOrder?.order_type || null,
              follower_name: purchaseOrder?.follower || null,
              buyer_account: purchaseOrder?.buyer_account || null,

              // 从采购订单同步的供应商信息
              supplier_id: product.suggestedSupplierId ? BigInt(product.suggestedSupplierId) :
                (purchaseOrder?.supplier_id ? BigInt(purchaseOrder.supplier_id) : null),
              supplier_name: product.suggestedSupplierName || purchaseOrder?.supplier_name || null,

              // 从采购订单同步的渠道信息
              sales_staff_name: purchaseOrder?.submitter || null, // 使用提交人作为销售员
              sales_department: null, // 需要根据业务逻辑设置，暂时为空

              // 从采购订单同步的地址信息（优先使用实际地址）
              delivery_address: purchaseOrder?.actual_address || purchaseOrder?.recipient_address || purchaseOrder?.delivery_address || null,

              // 初始状态
              after_sales_status: 0, // 待处理
              after_sales_progress: 0, // 跟进补发单号
              erp_status: 0, // 全部
              business_return: false,
              customer_service_return: false,
              is_own_brand: false, // 默认值，可根据供应商信息判断

              created_at: new Date(),
              updated_at: new Date()
            }
          });

          createdApplications.push(afterSalesApplication);
        }

        // 为每个售后申请创建对应的商品明细
        for (let i = 0; i < createdApplications.length; i++) {
          const afterSalesApplication = createdApplications[i];
          const product = products[i];

          // 创建售后商品项 - 每个售后申请对应一个商品
          await tx.after_sales_product_item.create({
            data: {
              after_sales_id: afterSalesApplication.id,
              purchase_order_item_id: product.originalId ? BigInt(product.originalId) : null,
              goods_spu_id: product.goodsSpuId ? BigInt(product.goodsSpuId) : null,
              product_name: product.productName,
              product_code: product.productCode || '',
              sku_code: product.sku || '',
              specification: product.specification || '',
              product_image: product.productImage || '',
              unit_price: product.unitPrice ? parseFloat(product.unitPrice) : null,
              sale_price: null, // 可以从采购订单中获取
              cost_price: null, // 可以从采购订单中获取
              quantity: product.quantity || 1,
              is_return: product.isReturn || false,
              return_quantity: product.isReturn ? (product.quantity || 1) : 0,
              // 供应商信息
              suggested_supplier_id: product.suggestedSupplierId ? BigInt(product.suggestedSupplierId) : null,
              suggested_supplier_name: product.suggestedSupplierName || '',
              actual_supplier_id: product.actualSupplierId ? BigInt(product.actualSupplierId) : null,
              actual_supplier_name: product.actualSupplierName || '',
              // 物流信息
              shipping_status: product.shippingStatus || 0,
              shipping_number: product.shippingNumber || '',
              shipping_info: product.shippingInfo || '',
              created_at: new Date(),
              updated_at: new Date()
            }
          });
        }

        // 为每个售后申请创建附件记录和操作日志
        for (const afterSalesApplication of createdApplications) {
          // 创建附件记录
          if (attachments && attachments.length > 0) {
            const attachmentItems = attachments.map(attachment => ({
              after_sales_id: afterSalesApplication.id,
              attachment_type: 'application',
              file_name: attachment.name || attachment.fileName || '未知文件',
              file_url: attachment.url || attachment,
              file_size: attachment.size || null,
              file_type: attachment.type || null,
              uploader_id: BigInt(currentUser.id),
              uploader_name: currentUser.nickname || currentUser.username,
              created_at: new Date(),
              updated_at: new Date()
            }));

            await tx.after_sales_attachment.createMany({
              data: attachmentItems
            });
          }

          // 创建操作日志
          await tx.after_sales_log.create({
            data: {
              after_sales_id: afterSalesApplication.id,
              operation_type: 'create',
              operation_content: '创建售后申请',
              new_value: JSON.stringify({
                afterSalesType,
                afterSalesContent,
                customerDemand
              }),
              operator_id: BigInt(currentUser.id),
              operator_name: currentUser.nickname || currentUser.username,
              operator_role: 'applicant',
              created_at: new Date()
            }
          });
        }

        return createdApplications;
      });

      return {
        data: {
          applications: results.map(result => ({
            id: result.id.toString(),
            afterSalesNumber: result.after_sales_number,
            orderNumber: result.original_order_number,
            afterSalesType: result.after_sales_type,
            afterSalesStatus: result.after_sales_status,
            createdAt: result.created_at
          })),
          totalCount: results.length
        },
        message: `成功创建${results.length}条售后申请记录`
      };

    } catch (error) {
      console.error('创建售后申请失败:', error);
      throw error;
    }
  }

  /**
   * 获取售后申请列表
   * @param {Object} filters - 过滤条件
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @returns {Promise<Object>} - 分页结果
   */
  async getAfterSalesApplications(filters = {}, page = 1, pageSize = 10) {
    try {
      const skip = (page - 1) * pageSize;

      // 构建基础查询条件
      const where = {
        deleted_at: null
      };

      // 处理各种搜索条件
      await this.buildSearchConditions(where, filters);

      // 调试日志
      console.log('搜索过滤条件:', JSON.stringify(filters, null, 2));
      console.log('最终查询条件:', JSON.stringify(where, null, 2));

      // 获取总数
      const total = await this.prisma.afterSalesApplication.count({
        where
      });

      // 获取列表数据
      const items = await this.prisma.afterSalesApplication.findMany({
        where,
        include: {
          product_items: {
            where: { deleted_at: null },
            select: {
              id: true,
              goods_spu_id: true,
              purchase_order_item_id: true,
              product_name: true,
              product_code: true,
              sku_code: true,
              specification: true,
              product_image: true,
              unit_price: true,
              sale_price: true,
              cost_price: true,
              quantity: true,
              is_return: true,
              return_quantity: true,
              // 供应商信息
              suggested_supplier_id: true,
              suggested_supplier_name: true,
              actual_supplier_id: true,
              actual_supplier_name: true,
              // 物流信息
              shipping_status: true,
              shipping_number: true,
              shipping_info: true,
              created_at: true,
              updated_at: true
            },
            orderBy: { created_at: 'asc' }
          },
          attachments: {
            where: { deleted_at: null },
            orderBy: { created_at: 'desc' }
          },
          reply_history: {
            where: { deleted_at: null },
            select: {
              id: true,
              is_return: true,
              created_at: true,
              responsibility: true
            },
            orderBy: { created_at: 'desc' }
          }
        },
        orderBy: { created_at: 'desc' },
        skip,
        take: pageSize
      });

      // 手动获取用户和部门信息
      const enrichedItems = await this.enrichItemsWithUserInfo(items);

      // 如果有回复时间搜索条件，需要进一步过滤结果
      let filteredItems = enrichedItems;
      if (filters.reply_start_time || filters.reply_end_time) {
        console.log('开始应用回复时间过滤条件...');

        filteredItems = enrichedItems.filter(item => {
          // 检查该售后申请是否有回复记录
          if (!item.reply_history || item.reply_history.length === 0) {
            console.log('售后申请ID:', item.id, '没有回复记录，过滤掉');
            return false; // 没有回复记录，不符合条件
          }

          // 获取最新的回复记录（已经按 created_at desc 排序）
          const latestReply = item.reply_history[0];
          const replyTime = new Date(latestReply.created_at);
          let matchesTimeRange = true;

          console.log('售后申请ID:', item.id, '最新回复时间:', replyTime.toISOString());

          if (filters.reply_start_time) {
            let startDate;
            if (isNaN(filters.reply_start_time)) {
              startDate = new Date(filters.reply_start_time);
            } else {
              const startTime = parseInt(filters.reply_start_time);
              startDate = startTime.toString().length === 10
                ? new Date(startTime * 1000)
                : new Date(startTime);
            }
            const startMatches = replyTime >= startDate;
            matchesTimeRange = matchesTimeRange && startMatches;
            console.log('开始时间检查:', startDate.toISOString(), '回复时间:', replyTime.toISOString(), '匹配:', startMatches);
          }

          if (filters.reply_end_time) {
            let endDate;
            if (isNaN(filters.reply_end_time)) {
              endDate = new Date(filters.reply_end_time);
            } else {
              const endTime = parseInt(filters.reply_end_time);
              endDate = endTime.toString().length === 10
                ? new Date(endTime * 1000)
                : new Date(endTime);
            }
            const endMatches = replyTime <= endDate;
            matchesTimeRange = matchesTimeRange && endMatches;
            console.log('结束时间检查:', endDate.toISOString(), '回复时间:', replyTime.toISOString(), '匹配:', endMatches);
          }

          console.log('售后申请ID:', item.id, '最终匹配结果:', matchesTimeRange);
          return matchesTimeRange;
        });

        console.log('回复时间过滤前数量:', enrichedItems.length, '过滤后数量:', filteredItems.length);
      }

      // 转换数据格式
      const formattedItems = await Promise.all(filteredItems.map(async item => {
        // 直接查找 attachment_type=reply 且 after_sales_id=当前申请id 的附件，并只取最新一条
        let reply_file = [];
        const latestReplyAttachment = item.attachments
          .filter(att => att.attachment_type === 'reply' && att.after_sales_id?.toString() === item.id.toString())
          .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0];
        if (latestReplyAttachment) {
          reply_file = [{
            id: latestReplyAttachment.id.toString(),
            fileName: latestReplyAttachment.file_name,
            fileUrl: latestReplyAttachment.file_url,
            fileSize: latestReplyAttachment.file_size,
            fileType: latestReplyAttachment.file_type,
            uploaderName: latestReplyAttachment.uploader_name,
            createdAt: latestReplyAttachment.created_at
          }];
        }
        const formatted = this.formatAfterSalesApplicationData(item);
        formatted.reply_file = reply_file;
        return formatted;
      }));

      // 如果进行了额外过滤，需要重新计算总数
      const finalTotal = filteredItems.length < enrichedItems.length ? filteredItems.length : total;

      return {
        items: formattedItems,
        total: finalTotal,
        page,
        pageSize,
        totalPages: Math.ceil(finalTotal / pageSize)
      };

    } catch (error) {
      console.error('获取售后申请列表失败:', error);
      throw error;
    }
  }

  /**
   * 构建搜索条件
   * @param {Object} where - 基础查询条件
   * @param {Object} filters - 搜索过滤条件
   */
  async buildSearchConditions(where, filters) {
    // 1. 售后编号搜索
    if (filters.after_sales_number) {
      // 处理控制器传来的对象格式 {contains: "售后编号"}
      if (typeof filters.after_sales_number === 'object' && filters.after_sales_number.contains) {
        where.after_sales_number = {
          contains: filters.after_sales_number.contains,
          mode: 'insensitive'
        };
      } else if (typeof filters.after_sales_number === 'string') {
        where.after_sales_number = {
          contains: filters.after_sales_number,
          mode: 'insensitive'
        };
      }
    }

    // 2. 订单号搜索 (通过 original_order_id 字段)
    if (filters.original_order_number) {
      // 处理控制器传来的对象格式 {contains: "订单号"}
      let orderNumberValue;
      if (typeof filters.original_order_number === 'object' && filters.original_order_number.contains) {
        orderNumberValue = filters.original_order_number.contains;
      } else if (typeof filters.original_order_number === 'string') {
        orderNumberValue = filters.original_order_number;
      }

      if (orderNumberValue) {
        // 将订单号转换为 BigInt 进行精确匹配
        try {
          const orderId = BigInt(orderNumberValue);
          where.original_order_id = orderId;
          console.log('订单号搜索条件:', orderNumberValue, '-> BigInt:', orderId);
        } catch (error) {
          console.warn('Invalid order number format:', orderNumberValue);
          // 如果转换失败，设置一个不可能的条件
          where.original_order_id = BigInt(-1);
        }
      }
    }

    // 3. 售后状态搜索
    if (filters.after_sales_status !== undefined && filters.after_sales_status !== null) {
      where.after_sales_status = filters.after_sales_status;
      console.log('售后状态搜索条件:', filters.after_sales_status);
    }

    // 4. 售后类型搜索
    if (filters.after_sales_type) {
      where.after_sales_type = {
        contains: filters.after_sales_type,
        mode: 'insensitive'
      };
    }

    // 5. 实际收货地址搜索
    if (filters.delivery_address) {
      where.delivery_address = {
        contains: filters.delivery_address,
        mode: 'insensitive'
      };
    }

    // 6. 售后时间范围搜索 (时间戳格式)
    if (filters.created_at_start || filters.created_at_end) {
      where.created_at = {};
      if (filters.created_at_start) {
        let startTime;
        if (isNaN(filters.created_at_start)) {
          // ISO字符串格式或日期字符串
          startTime = new Date(filters.created_at_start);
        } else {
          const timestamp = parseInt(filters.created_at_start);
          // 判断是10位时间戳（秒）还是13位时间戳（毫秒）
          startTime = timestamp.toString().length === 10
            ? new Date(timestamp * 1000)
            : new Date(timestamp);
        }
        where.created_at.gte = startTime;
        console.log('开始时间搜索条件:', filters.created_at_start, '->', startTime.toISOString(), '(本地时间:', startTime.toLocaleString('zh-CN'), ')');
      }
      if (filters.created_at_end) {
        let endTime;
        if (isNaN(filters.created_at_end)) {
          // ISO字符串格式或日期字符串
          endTime = new Date(filters.created_at_end);
        } else {
          const timestamp = parseInt(filters.created_at_end);
          // 判断是10位时间戳（秒）还是13位时间戳（毫秒）
          endTime = timestamp.toString().length === 10
            ? new Date(timestamp * 1000)
            : new Date(timestamp);
        }
        where.created_at.lte = endTime;
        console.log('结束时间搜索条件:', filters.created_at_end, '->', endTime.toISOString(), '(本地时间:', endTime.toLocaleString('zh-CN'), ')');
      }
    }

    // 7. 销售部门搜索 (通过部门名称查找 applicant_id)
    if (filters.dept_name) {
      // 先通过部门名称查找部门ID
      const departments = await this.prisma.baseSystemDept.findMany({
        where: {
          name: filters.dept_name,
          deleted_at: null
        },
        select: { id: true }
      });

      if (departments.length > 0) {
        const deptIds = departments.map(dept => dept.id);

        // 再通过部门ID查找该部门下的所有用户
        const deptUsers = await this.prisma.baseSystemUser.findMany({
          where: {
            dept_id: {
              in: deptIds
            },
            deleted_at: null
          },
          select: { id: true }
        });

        if (deptUsers.length > 0) {
          where.applicant_id = {
            in: deptUsers.map(user => user.id)
          };
        } else {
          // 如果部门下没有用户，设置一个不可能的条件
          where.applicant_id = BigInt(-1);
        }
        console.log('销售部门搜索条件:', filters.dept_name, '-> 部门数:', departments.length, '用户数:', deptUsers.length);
      } else {
        // 如果找不到部门，设置一个不可能的条件
        where.applicant_id = BigInt(-1);
        console.log('销售部门搜索条件:', filters.dept_name, '-> 部门不存在');
      }
    }

    // 兼容旧的 dept_id 参数
    if (filters.dept_id && !filters.dept_name) {
      const deptUsers = await this.prisma.baseSystemUser.findMany({
        where: {
          dept_id: BigInt(filters.dept_id),
          deleted_at: null
        },
        select: { id: true }
      });

      if (deptUsers.length > 0) {
        where.applicant_id = {
          in: deptUsers.map(user => user.id)
        };
      } else {
        where.applicant_id = BigInt(-1);
      }
      console.log('销售部门ID搜索条件:', filters.dept_id, '-> 用户数:', deptUsers.length);
    }

    // 5. 售后员搜索 (通过用户名查找 after_sales_staff_id)
    if (filters.after_sales_staff_username) {
      const staffUser = await this.prisma.baseSystemUser.findFirst({
        where: {
          username: filters.after_sales_staff_username,
          deleted_at: null
        },
        select: { id: true }
      });

      if (staffUser) {
        where.after_sales_staff_id = staffUser.id;
      } else {
        where.after_sales_staff_id = BigInt(-1);
      }
      console.log('售后员搜索条件:', filters.after_sales_staff_username, '->', staffUser?.id || 'not found');
    }

    // 6. 提出人搜索 (通过用户名查找 applicant_id)
    if (filters.proposer_username) {
      const proposerUser = await this.prisma.baseSystemUser.findFirst({
        where: {
          username: filters.proposer_username,
          deleted_at: null
        },
        select: { id: true }
      });

      if (proposerUser) {
        where.applicant_id = proposerUser.id;
      } else {
        where.applicant_id = BigInt(-1);
      }
      console.log('提出人搜索条件:', filters.proposer_username, '->', proposerUser?.id || 'not found');
    }

    // 7. 采购员搜索 (通过用户名查找 purchaser_id)
    if (filters.purchaser_username) {
      const purchaserUser = await this.prisma.baseSystemUser.findFirst({
        where: {
          username: filters.purchaser_username,
          deleted_at: null
        },
        select: { id: true }
      });

      if (purchaserUser) {
        where.purchaser_id = purchaserUser.id;
      } else {
        where.purchaser_id = BigInt(-1);
      }
    }

    // 8. 跟单员搜索 (通过用户名查找用户ID，匹配 follower_name 字段)
    if (filters.follower) {
      // 通过用户名查找用户ID
      const followerUser = await this.prisma.baseSystemUser.findFirst({
        where: {
          username: filters.follower,
          deleted_at: null
        },
        select: { id: true }
      });

      if (followerUser) {
        // 直接在售后申请表中通过 follower_name 字段搜索 (转换为字符串)
        where.follower_name = followerUser.id.toString();
        console.log('跟单员搜索条件:', filters.follower, '-> 用户ID:', followerUser.id, '-> 字符串:', followerUser.id.toString());
      } else {
        // 如果找不到用户，设置一个不可能的条件
        where.follower_name = '-1';
        console.log('跟单员搜索条件:', filters.follower, '-> 用户不存在');
      }
    }

    // 9. 商品相关搜索条件 (需要通过 product_items 关联)
    const productItemConditions = [];

    // 供应商名称
    if (filters.supplier_name) {
      productItemConditions.push({
        suggested_supplier_name: {
          contains: filters.supplier_name,
          mode: 'insensitive'
        }
      });
    }

    // 商品名称
    if (filters.product_name) {
      productItemConditions.push({
        product_name: {
          contains: filters.product_name,
          mode: 'insensitive'
        }
      });
    }

    // 商品编码
    if (filters.product_code) {
      productItemConditions.push({
        product_code: {
          contains: filters.product_code,
          mode: 'insensitive'
        }
      });
    }

    // 寄出单号 - 搜索 after_sales_application 表的 return_number 字段
    if (filters.shipping_number) {
      where.return_number = {
        contains: filters.shipping_number,
        mode: 'insensitive'
      };
      console.log('寄出单号搜索条件:', filters.shipping_number, '-> return_number字段');
    }

    // 如果有商品相关的搜索条件，添加到主查询中
    if (productItemConditions.length > 0) {
      where.product_items = {
        some: {
          AND: [
            { deleted_at: null },
            ...productItemConditions
          ]
        }
      };
    }

    // 10. 回复历史相关搜索条件 (需要通过 reply_history 关联)
    const replyHistoryConditions = [];

    // 客服是否退货 (搜索 reply_history 表的 is_return 字段，取最新一条记录)
    if (filters.is_return !== undefined) {
      let returnValue;
      const inputValue = filters.is_return.toString().toLowerCase();

      // 支持多种输入格式转换为布尔值
      if (inputValue === 't' || inputValue === 'true' || inputValue === '1' || inputValue === 'yes') {
        returnValue = true;
      } else if (inputValue === 'f' || inputValue === 'false' || inputValue === '0' || inputValue === 'no') {
        returnValue = false;
      } else {
        // 如果是其他值，尝试转换为布尔值
        returnValue = Boolean(filters.is_return);
      }

      replyHistoryConditions.push({
        is_return: returnValue
      });
      console.log('客服是否退货搜索条件 (reply_history):', filters.is_return, '->', returnValue, '(类型:', typeof returnValue, ')');
    }

    // 回复时间范围搜索在后处理中进行，这里不添加数据库查询条件
    // 因为需要针对最新一条回复记录进行时间过滤

    // 如果有回复历史相关的搜索条件，添加到主查询中
    if (replyHistoryConditions.length > 0) {
      where.reply_history = {
        some: {
          AND: [
            { deleted_at: null },
            ...replyHistoryConditions
          ]
        }
      };

      console.log('回复历史搜索条件已应用，条件数量:', replyHistoryConditions.length);
    }
  }

  /**
   * 为售后申请列表项添加用户和部门信息
   * @param {Array} items - 售后申请列表
   * @returns {Array} 包含用户和部门信息的列表
   */
  async enrichItemsWithUserInfo(items) {
    if (!items || items.length === 0) {
      return items;
    }

    // 收集所有需要查询的用户ID
    const userIds = new Set();
    items.forEach(item => {
      if (item.applicant_id) userIds.add(item.applicant_id);
      if (item.purchaser_id) userIds.add(item.purchaser_id);
      if (item.after_sales_staff_id) userIds.add(item.after_sales_staff_id);
      // 添加跟单员ID (follower_name字段存储的是用户ID的字符串形式)
      if (item.follower_name) {
        try {
          const followerId = BigInt(item.follower_name);
          userIds.add(followerId);
        } catch (error) {
          console.warn('Invalid follower_name format:', item.follower_name);
        }
      }
    });

    // 批量查询用户信息
    const users = await this.prisma.baseSystemUser.findMany({
      where: {
        id: { in: Array.from(userIds) },
        deleted_at: null
      },
      select: {
        id: true,
        username: true,
        nickname: true,
        dept_id: true
      }
    });

    // 收集所有需要查询的部门ID
    const deptIds = new Set();
    users.forEach(user => {
      if (user.dept_id) deptIds.add(user.dept_id);
    });

    // 批量查询部门信息
    const departments = await this.prisma.baseSystemDept.findMany({
      where: {
        id: { in: Array.from(deptIds) },
        deleted_at: null
      },
      select: {
        id: true,
        name: true
      }
    });

    // 创建查找映射
    const userMap = new Map();
    users.forEach(user => {
      userMap.set(user.id.toString(), user);
    });

    const deptMap = new Map();
    departments.forEach(dept => {
      deptMap.set(dept.id.toString(), dept);
    });

    // 为每个售后申请项添加用户和部门信息
    return items.map(item => {
      const enrichedItem = { ...item };

      // 添加申请人信息
      if (item.applicant_id) {
        const applicant = userMap.get(item.applicant_id.toString());
        if (applicant) {
          enrichedItem.applicant = applicant;
          // 添加申请人的部门信息
          if (applicant.dept_id) {
            const dept = deptMap.get(applicant.dept_id.toString());
            if (dept) {
              enrichedItem.applicant.dept = dept;
            }
          }
        }
      }

      // 添加采购员信息
      if (item.purchaser_id) {
        const purchaser = userMap.get(item.purchaser_id.toString());
        if (purchaser) {
          enrichedItem.purchaser = purchaser;
        }
      }

      // 添加售后员信息
      if (item.after_sales_staff_id) {
        const afterSalesStaff = userMap.get(item.after_sales_staff_id.toString());
        if (afterSalesStaff) {
          enrichedItem.after_sales_staff = afterSalesStaff;
        }
      }

      // 添加跟单员信息 (follower_name字段存储的是用户ID的字符串形式)
      if (item.follower_name) {
        const follower = userMap.get(item.follower_name);
        if (follower) {
          enrichedItem.follower = follower;
        }
      }

      return enrichedItem;
    });
  }

  /**
   * 根据ID获取售后申请详情
   * @param {string|number} id - 售后申请ID
   * @returns {Promise<Object>} - 售后申请详情
   */
  async getAfterSalesApplicationById(id) {
    try {
      const afterSalesApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        },
        include: {
          product_items: {
            where: { deleted_at: null },
            select: {
              id: true,
              purchase_order_item_id: true,
              product_name: true,
              product_code: true,
              sku_code: true,
              specification: true,
              product_image: true,
              unit_price: true,
              sale_price: true,
              cost_price: true,
              quantity: true,
              is_return: true,
              return_quantity: true,
              // 供应商信息
              suggested_supplier_id: true,
              suggested_supplier_name: true,
              actual_supplier_id: true,
              actual_supplier_name: true,
              // 物流信息
              shipping_status: true,
              shipping_number: true,
              shipping_info: true,
              created_at: true,
              updated_at: true
            },
            orderBy: { created_at: 'asc' }
          },
          attachments: {
            where: { deleted_at: null },
            orderBy: { created_at: 'desc' }
          },
          reply_history: {
            where: { deleted_at: null },
            orderBy: { created_at: 'desc' }
          },
          logs: {
            orderBy: { created_at: 'desc' },
            take: 50
          }
        }
      });

      if (!afterSalesApplication) {
        throw new Error('售后申请不存在');
      }

      return this.formatAfterSalesApplicationData(afterSalesApplication);

    } catch (error) {
      console.error('获取售后申请详情失败:', error);
      throw error;
    }
  }

  /**
   * 更新售后申请状态
   * @param {string|number} id - 售后申请ID
   * @param {Object} updateData - 更新数据
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 更新结果
   */
  async updateAfterSalesStatus(id, updateData, currentUser) {
    try {
      const { status, progress, remark } = updateData;

      // 获取当前售后申请信息
      const currentApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!currentApplication) {
        throw new Error('售后申请不存在');
      }

      // 开始事务更新
      const result = await this.prisma.$transaction(async (tx) => {
        // 构建更新数据
        const updateFields = {
          updated_at: new Date()
        };

        if (status !== undefined) {
          updateFields.after_sales_status = parseInt(status);
        }
        if (progress !== undefined) {
          updateFields.after_sales_progress = progress;
        }
        if (remark !== undefined) {
          updateFields.remark = remark;
        }

        // 更新售后申请
        const updatedApplication = await tx.afterSalesApplication.update({
          where: { id: BigInt(id) },
          data: updateFields
        });

        // 记录操作日志
        const logData = {
          after_sales_id: BigInt(id),
          operation_type: 'update_status',
          operation_content: '更新售后状态',
          operator_id: BigInt(currentUser.id),
          operator_name: currentUser.nickname || currentUser.username,
          operator_role: 'staff',
          created_at: new Date()
        };

        // 记录变更内容
        const changes = [];
        if (status !== undefined && currentApplication.afterSalesStatus !== parseInt(status)) {
          changes.push(`状态: ${currentApplication.afterSalesStatus} -> ${status}`);
          logData.oldValue = currentApplication.afterSalesStatus.toString();
          logData.newValue = status.toString();
        }
        if (progress !== undefined && currentApplication.afterSalesProgress !== progress) {
          changes.push(`进度: ${currentApplication.afterSalesProgress} -> ${progress}`);
        }
        if (remark !== undefined) {
          changes.push(`备注: ${remark}`);
        }

        if (changes.length > 0) {
          logData.operation_content = `更新售后状态: ${changes.join(', ')}`;
          await tx.after_sales_log.create({ data: logData });
        }

        return updatedApplication;
      });

      return this.formatAfterSalesApplicationData(result);

    } catch (error) {
      console.error('更新售后申请状态失败:', error);
      throw error;
    }
  }

  /**
   * 添加售后回复
   * @param {string|number} id - 售后申请ID
   * @param {Object} replyData - 回复数据
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 回复结果
   */
  async addAfterSalesReply(id, replyData, currentUser) {
    try {
      const {
        replyContent,
        attachments = [],
        responsibility,
        companyCostType,
        companyCostAmount,
        supplierCostType,
        supplierCostAmount,
        totalCostAmount,
        isReturn
      } = replyData;

      // 检查售后申请是否存在
      const afterSalesApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!afterSalesApplication) {
        throw new Error('售后申请不存在');
      }

      // 开始事务添加回复
      const result = await this.prisma.$transaction(async (tx) => {
        // 更新售后申请的回复信息
        await tx.afterSalesApplication.update({
          where: { id: BigInt(id) },
          data: {
            reply_content: replyContent,
            reply_time: new Date(),
            replier_id: BigInt(currentUser.id),
            replier_name: currentUser.nickname || currentUser.username,
            updated_at: new Date()
          }
        });
        console.log('sdsasdasdasdasd', {
          companyCostType,
          companyCostAmount,
          supplierCostType,
          supplierCostAmount,
          totalCostAmount
        })
        const company_cost_type_temp = parseFloat(companyCostType) ?? null;
        const company_cost_amount_temp = parseFloat(companyCostAmount) ?? null;
        const supplier_cost_type_temp = parseFloat(supplierCostType) ?? null;
        const supplier_cost_amount_temp = parseFloat(supplierCostAmount) ?? null;
        const total_cost_amount_temp = parseFloat(totalCostAmount) ?? null;
        if (company_cost_amount_temp && company_cost_amount_temp < 0) {
          throw new Error('我司承担费用金额不能为负数');
        }
        if (supplier_cost_amount_temp && supplier_cost_amount_temp < 0) {
          throw new Error('供应商承担费用金额不能为负数');
        }
        if (total_cost_amount_temp && total_cost_amount_temp < 0) {
          throw new Error('总费用金额不能为负数');
        }
        if (company_cost_amount_temp && supplier_cost_amount_temp && total_cost_amount_temp) {
          if (company_cost_amount_temp + supplier_cost_amount_temp !== total_cost_amount_temp) {
            throw new Error('总费用金额必须等于我司承担费用金额与供应商承担费用金额之和');
          }
        }
        console.log('添加回复历史记录:', {
          after_sales_id: BigInt(id),
          reply_content: replyContent,
          reply_type: 'staff',
          replier_id: BigInt(currentUser.id),
          replier_name: currentUser.nickname || currentUser.username,
          replier_role: 'staff',
          responsibility: parseInt(responsibility) || null,
          company_cost_type: company_cost_type_temp,
          company_cost_amount: company_cost_amount_temp,
          supplier_cost_type: supplier_cost_type_temp,
          supplier_cost_amount: supplier_cost_amount_temp,
          total_cost_amount: total_cost_amount_temp,
          is_return: isReturn !== undefined && isReturn !== null ? Boolean(isReturn) : false,
          reply_attachments: attachments && attachments.length > 0 ? JSON.stringify(attachments) : null,
          created_at: new Date(),
          updated_at: new Date()
        });
        // 添加回复历史记录
        const replyHistory = await tx.after_sales_reply_history.create({
          data: {
            after_sales_id: BigInt(id),
            reply_content: replyContent,
            reply_type: 'staff',
            replier_id: BigInt(currentUser.id),
            replier_name: currentUser.nickname || currentUser.username,
            replier_role: 'staff',

            // 责任归属和费用信息
            responsibility: parseInt(responsibility) || null,
            company_cost_type: company_cost_type_temp,
            company_cost_amount: company_cost_amount_temp,
            supplier_cost_type: supplier_cost_type_temp,
            supplier_cost_amount: supplier_cost_amount_temp,
            total_cost_amount: total_cost_amount_temp,
            is_return: isReturn !== undefined && isReturn !== null ? Boolean(isReturn) : false,
            reply_attachments: attachments && attachments.length > 0 ? JSON.stringify(attachments) : null,

            created_at: new Date(),
            updated_at: new Date()
          }
        });

        // 添加回复附件
        if (attachments && attachments.length > 0) {
          const attachmentItems = attachments.map(attachment => ({
            after_sales_id: BigInt(id),
            attachment_type: 'reply',
            file_name: attachment.name || attachment.fileName || '未知文件',
            file_url: attachment.url || attachment,
            file_size: attachment.size || null,
            file_type: attachment.type || null,
            uploader_id: BigInt(currentUser.id),
            uploader_name: currentUser.nickname || currentUser.username,
            created_at: new Date(),
            updated_at: new Date()
          }));

          await tx.after_sales_attachment.createMany({
            data: attachmentItems
          });
        }

        // 记录操作日志
        await tx.after_sales_log.create({
          data: {
            after_sales_id: BigInt(id),
            operation_type: 'reply',
            operation_content: '添加售后回复',
            new_value: replyContent,
            operator_id: BigInt(currentUser.id),
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: 'staff',
            created_at: new Date()
          }
        });

        return replyHistory;
      });

      return this.formatReplyHistoryData(result);

    } catch (error) {
      console.error('添加售后回复失败:', error);
      throw error;
    }
  }

  /**
   * 添加售后备注
   * @param {string|number} id - 售后申请ID
   * @param {Object} remarkData - 备注数据
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 添加结果
   */
  async addAfterSalesRemark(id, remarkData, currentUser) {
    try {
      const { remark, attachments = [] } = remarkData;

      // 获取当前售后申请信息
      const currentApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!currentApplication) {
        throw new Error('售后申请不存在');
      }

      // 开始事务添加备注
      const result = await this.prisma.$transaction(async (tx) => {
        // 更新售后申请的备注信息
        const updatedApplication = await tx.afterSalesApplication.update({
          where: { id: BigInt(id) },
          data: {
            remark: remark,
            updated_at: new Date()
          }
        });

        // 记录操作日志
        await tx.after_sales_log.create({
          data: {
            after_sales_id: BigInt(id),
            operation_type: 'add_remark',
            operation_content: `添加售后备注: ${remark}`,
            old_value: currentApplication.remark || '',
            new_value: remark,
            operator_id: BigInt(currentUser.id),
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: 'staff',
            created_at: new Date()
          }
        });

        // 如果有附件，保存附件信息
        if (attachments && attachments.length > 0) {
          for (const attachment of attachments) {
            await tx.after_sales_attachment.create({
              data: {
                after_sales_id: BigInt(id),
                file_name: attachment.name,
                file_url: attachment.url,
                file_size: attachment.size || 0,
                file_type: attachment.type || '',
                attachment_type: 'remark',
                created_at: new Date()
              }
            });
          }
        }

        return updatedApplication;
      });

      return this.formatAfterSalesApplicationData(result);

    } catch (error) {
      console.error('添加售后备注失败:', error);
      throw error;
    }
  }

  /**
   * 更换售后员
   * @param {string|number} id - 售后申请ID
   * @param {Object} staffData - 售后员数据
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 更换结果
   */
  async changeAfterSalesStaff(id, staffData, currentUser) {
    try {
      const { newStaffId, newStaffName, changeReason } = staffData;

      // 获取当前售后申请信息
      const currentApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!currentApplication) {
        throw new Error('售后申请不存在');
      }

      // 开始事务更换售后员
      const result = await this.prisma.$transaction(async (tx) => {
        // 更新售后申请的售后员信息
        const updatedApplication = await tx.afterSalesApplication.update({
          where: { id: BigInt(id) },
          data: {
            after_sales_staff_id: newStaffId ? BigInt(newStaffId) : null,
            after_sales_staff_name: newStaffName,
            updated_at: new Date()
          }
        });

        // 记录操作日志
        await tx.after_sales_log.create({
          data: {
            after_sales_id: BigInt(id),
            operation_type: 'change_staff',
            operation_content: `更换售后员: ${currentApplication.after_sales_staff_name} -> ${newStaffName}${changeReason ? `, 原因: ${changeReason}` : ''}`,
            old_value: currentApplication.after_sales_staff_name,
            new_value: newStaffName,
            operator_id: BigInt(currentUser.id),
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: 'staff',
            created_at: new Date()
          }
        });

        return updatedApplication;
      });

      return this.formatAfterSalesApplicationData(result);

    } catch (error) {
      console.error('更换售后员失败:', error);
      throw error;
    }
  }

  /**
   * 单独更新售后状态
   * @param {string|number} id - 售后申请ID
   * @param {number} afterSalesStatus - 新的售后状态
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 更新结果
   */
  async updateAfterSalesStatusOnly(id, afterSalesStatus, currentUser) {
    try {
      // 获取当前售后申请信息
      const currentApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!currentApplication) {
        throw new Error('售后申请不存在');
      }

      // 开始事务更新
      const result = await this.prisma.$transaction(async (tx) => {
        // 更新售后申请状态
        const updatedApplication = await tx.afterSalesApplication.update({
          where: { id: BigInt(id) },
          data: {
            after_sales_status: afterSalesStatus,
            updated_at: new Date()
          }
        });

        // 记录操作日志
        await tx.after_sales_log.create({
          data: {
            after_sales_id: BigInt(id),
            operation_type: 'update_status',
            operation_content: `更新售后状态: ${currentApplication.after_sales_status} -> ${afterSalesStatus}`,
            old_value: currentApplication.after_sales_status.toString(),
            new_value: afterSalesStatus.toString(),
            operator_id: BigInt(currentUser.id),
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: 'staff',
            created_at: new Date()
          }
        });

        return updatedApplication;
      });

      return this.formatAfterSalesApplicationData(result);

    } catch (error) {
      console.error('更新售后状态失败:', error);
      throw error;
    }
  }

  /**
   * 单独更新售后进度
   * @param {string|number} id - 售后申请ID
   * @param {number} afterSalesProgress - 新的售后进度
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 更新结果
   */
  async updateAfterSalesProgress(id, afterSalesProgress, currentUser) {
    try {
      // 获取当前售后申请信息
      const currentApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!currentApplication) {
        throw new Error('售后申请不存在');
      }

      // 开始事务更新
      const result = await this.prisma.$transaction(async (tx) => {
        // 更新售后申请进度
        const updatedApplication = await tx.afterSalesApplication.update({
          where: { id: BigInt(id) },
          data: {
            after_sales_progress: afterSalesProgress,
            updated_at: new Date()
          }
        });

        // 记录操作日志
        await tx.after_sales_log.create({
          data: {
            after_sales_id: BigInt(id),
            operation_type: 'update_progress',
            operation_content: `更新售后进度: ${currentApplication.after_sales_progress} -> ${afterSalesProgress}`,
            old_value: currentApplication.after_sales_progress.toString(),
            new_value: afterSalesProgress.toString(),
            operator_id: BigInt(currentUser.id),
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: 'staff',
            created_at: new Date()
          }
        });

        return updatedApplication;
      });

      return this.formatAfterSalesApplicationData(result);

    } catch (error) {
      console.error('更新售后进度失败:', error);
      throw error;
    }
  }

  /**
   * 更新退货地址
   * @param {string|number} id - 售后申请ID
   * @param {string} returnAddress - 新的退货地址
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 更新结果
   */
  async updateReturnAddress(id, returnAddress, currentUser) {
    try {
      // 获取当前售后申请信息
      const currentApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!currentApplication) {
        throw new Error('售后申请不存在');
      }

      // 开始事务更新
      const result = await this.prisma.$transaction(async (tx) => {
        // 更新退货地址
        const updatedApplication = await tx.afterSalesApplication.update({
          where: { id: BigInt(id) },
          data: {
            return_address: returnAddress,
            updated_at: new Date()
          }
        });

        // 记录操作日志
        await tx.after_sales_log.create({
          data: {
            after_sales_id: BigInt(id),
            operation_type: 'update_return_address',
            operation_content: `更新退货地址: ${currentApplication.return_address || '空'} -> ${returnAddress || '空'}`,
            old_value: currentApplication.return_address || '',
            new_value: returnAddress || '',
            operator_id: BigInt(currentUser.id),
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: 'staff',
            created_at: new Date()
          }
        });

        return updatedApplication;
      });

      return this.formatAfterSalesApplicationData(result);

    } catch (error) {
      console.error('更新退货地址失败:', error);
      throw error;
    }
  }

  /**
   * 更新退货地址
   * @param {string|number} id - 售后申请ID
   * @param {string} returnAddress - 新的退货地址
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 更新结果
   */
  async updateReturnNumber(id, returnNumber, currentUser) {
    try {
      // 获取当前售后申请信息
      const currentApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!currentApplication) {
        throw new Error('售后申请不存在');
      }

      // 开始事务更新
      const result = await this.prisma.$transaction(async (tx) => {
        // 更新退货地址
        const updatedApplication = await tx.afterSalesApplication.update({
          where: { id: BigInt(id) },
          data: {
            return_number: returnNumber,
            updated_at: new Date()
          }
        });

        // 记录操作日志
        await tx.after_sales_log.create({
          data: {
            after_sales_id: BigInt(id),
            operation_type: 'update_return_number',
            operation_content: `更新退货单号: ${currentApplication.return_number || '空'} -> ${returnNumber || '空'}`,
            old_value: currentApplication.return_address || '',
            new_value: returnNumber || '',
            operator_id: BigInt(currentUser.id),
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: 'staff',
            created_at: new Date()
          }
        });

        return updatedApplication;
      });

      return this.formatAfterSalesApplicationData(result);

    } catch (error) {
      console.error('更新退货地址失败:', error);
      throw error;
    }
  }

  /**
   * 更新ERP系统状态
   * @param {string|number} id - 售后申请ID
   * @param {number} erpStatus - 新的ERP状态
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 更新结果
   */
  async updateErpStatus(id, erpStatus, currentUser) {
    try {
      // 获取当前售后申请信息
      const currentApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!currentApplication) {
        throw new Error('售后申请不存在');
      }

      // 开始事务更新
      const result = await this.prisma.$transaction(async (tx) => {
        // 更新ERP状态
        const updatedApplication = await tx.afterSalesApplication.update({
          where: { id: BigInt(id) },
          data: {
            erp_status: erpStatus,
            updated_at: new Date()
          }
        });

        // 记录操作日志
        await tx.after_sales_log.create({
          data: {
            after_sales_id: BigInt(id),
            operation_type: 'update_erp_status',
            operation_content: `更新ERP状态: ${currentApplication.erp_status} -> ${erpStatus}`,
            old_value: currentApplication.erp_status.toString(),
            new_value: erpStatus.toString(),
            operator_id: BigInt(currentUser.id),
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: 'staff',
            created_at: new Date()
          }
        });

        return updatedApplication;
      });

      return this.formatAfterSalesApplicationData(result);

    } catch (error) {
      console.error('更新ERP状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取回复组件所需数据
   * @param {string|number} id - 售后申请ID
   * @returns {Promise<Object>} - 回复组件数据
   */
  async getReplyComponentData(id) {
    try {
      // 获取售后申请详情
      const afterSalesApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        },
        include: {
          // 包含商品项
          product_items: {
            where: { deleted_at: null },
            orderBy: { created_at: 'asc' }
          },
          // 包含附件
          attachments: {
            where: {
              deleted_at: null,
              attachment_type: 'application' // 只获取申请时的附件
            },
            orderBy: { created_at: 'asc' }
          },
          // 包含最新的回复历史（如果有）
          reply_history: {
            where: { deleted_at: null },
            orderBy: { created_at: 'desc' },
            take: 1
          }
        }
      });

      if (!afterSalesApplication) {
        throw new Error('售后申请不存在');
      }

      // 格式化返回数据
      const result = {
        // 基本信息
        id: afterSalesApplication.id.toString(),
        afterSalesNumber: afterSalesApplication.after_sales_number,
        originalOrderNumber: afterSalesApplication.original_order_number,
        purchaseOrderNumber: afterSalesApplication.purchase_order_number,

        // 售后信息
        afterSalesType: afterSalesApplication.after_sales_type,
        afterSalesContent: afterSalesApplication.after_sales_content,
        customerDemand: afterSalesApplication.customer_demand,

        // 人员信息
        applicantName: afterSalesApplication.applicant_name,
        purchaserName: afterSalesApplication.purchaser_name,
        afterSalesStaffName: afterSalesApplication.after_sales_staff_name,
        followerName: afterSalesApplication.follower_name,
        salesStaffName: afterSalesApplication.sales_staff_name,
        salesDepartment: afterSalesApplication.sales_department,

        // 订单信息
        orderSource: afterSalesApplication.order_source,
        orderType: afterSalesApplication.order_type,
        buyerAccount: afterSalesApplication.buyer_account,
        deliveryAddress: afterSalesApplication.delivery_address,

        // 供应商信息
        supplierName: afterSalesApplication.supplier_name,
        isOwnBrand: afterSalesApplication.is_own_brand,

        // 状态信息
        afterSalesStatus: afterSalesApplication.after_sales_status,
        afterSalesProgress: afterSalesApplication.after_sales_progress,
        businessReturn: afterSalesApplication.business_return,
        customerServiceReturn: afterSalesApplication.customer_service_return,
        returnAddress: afterSalesApplication.return_address,
        returnNumber: afterSalesApplication.return_number,

        // 回复信息
        replyContent: afterSalesApplication.reply_content,
        replyTime: afterSalesApplication.reply_time,
        replierName: afterSalesApplication.replier_name,

        // 责任归属
        responsibility: afterSalesApplication.responsibility,

        // ERP状态
        erpStatus: afterSalesApplication.erp_status,

        // 备注
        remark: afterSalesApplication.remark,

        // 商品列表
        products: afterSalesApplication.product_items.map(item => ({
          id: item.id.toString(),
          productName: item.product_name,
          productCode: item.product_code,
          skuCode: item.sku_code,
          specification: item.specification,
          productImage: item.product_image,
          unitPrice: item.unit_price ? parseFloat(item.unit_price) : null,
          salePrice: item.sale_price ? parseFloat(item.sale_price) : null,
          costPrice: item.cost_price ? parseFloat(item.cost_price) : null,
          quantity: item.quantity,
          isReturn: item.is_return,
          returnQuantity: item.return_quantity
        })),

        // 附件列表
        attachments: afterSalesApplication.attachments.map(attachment => ({
          id: attachment.id.toString(),
          fileName: attachment.file_name,
          fileUrl: attachment.file_url,
          fileSize: attachment.file_size,
          fileType: attachment.file_type,
          uploaderName: attachment.uploader_name,
          createdAt: attachment.created_at
        })),

        // 最新回复信息（如果有）
        latestReply: afterSalesApplication.reply_history.length > 0 ?
          this.formatReplyHistoryData(afterSalesApplication.reply_history[0]) : null,

        // 时间信息
        createdAt: afterSalesApplication.created_at,
        updatedAt: afterSalesApplication.updated_at
      };

      return result;

    } catch (error) {
      console.error('获取回复组件数据失败:', error);
      throw error;
    }
  }

  /**
   * 根据订单ID获取回复历史记录
   * @param {string|number} orderId - 订单ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} - 回复历史记录列表
   */
  async getReplyHistoryByOrderId(orderId, options = {}) {
    try {
      const { page = 1, pageSize = 10 } = options;
      const skip = (page - 1) * pageSize;

      // 首先验证订单是否存在售后申请
      const afterSalesApplications = await this.prisma.afterSalesApplication.findMany({
        where: {
          original_order_id: BigInt(orderId),
          deleted_at: null
        },
        select: {
          id: true
        }
      });

      if (!afterSalesApplications || afterSalesApplications.length === 0) {
        return {
          items: [],
          total: 0,
          page,
          pageSize,
          totalPages: 0
        };
      }

      const afterSalesIds = afterSalesApplications.map(app => app.id);

      // 使用JOIN方式查询回复历史记录总数
      const total = await this.prisma.after_sales_reply_history.count({
        where: {
          after_sales_id: {
            in: afterSalesIds
          },
          deleted_at: null
        }
      });

      // 使用JOIN方式查询回复历史记录列表
      const replyHistoryList = await this.prisma.after_sales_reply_history.findMany({
        where: {
          after_sales_id: {
            in: afterSalesIds
          },
          deleted_at: null
        },
        include: {
          after_sales_application: {
            select: {
              id: true,
              after_sales_number: true,
              original_order_id: true,
              original_order_number: true
            }
          },
          replier: {
            select: {
              id: true,
              username: true
            }
          }
        },
        orderBy: {
          created_at: 'desc' // 按创建时间倒序排列，最新的在前面
        },
        skip,
        take: pageSize
      });

      // 格式化回复历史数据
      const formattedList = replyHistoryList.map(item => {
        const formattedItem = this.formatReplyHistoryData(item);

        // 添加一些额外的显示字段和售后申请信息
        return {
          ...formattedItem,
          // 售后申请相关信息
          afterSalesNumber: item.after_sales_application?.after_sales_number || '',
          originalOrderId: item.after_sales_application?.original_order_id?.toString() || '',
          originalOrderNumber: item.after_sales_application?.original_order_number || '',
          // 格式化显示字段
          modifyTime: this.formatDateTime(item.created_at),
          modifier: item.replier_name || '系统',
          companyAmount: item.company_cost_amount ? `${parseFloat(item.company_cost_amount).toFixed(2)}` : '-',
          supplierAmount: item.supplier_cost_amount ? `${parseFloat(item.supplier_cost_amount).toFixed(2)}` : '-',
          supplierAfterSalesAmount: item.supplier_cost_amount ? `${parseFloat(item.supplier_cost_amount).toFixed(2)}` : '-',
          supplierCompensation: this.getCostTypeText(item.supplier_cost_type) || '-',
          totalAmount: item.total_cost_amount ? `${parseFloat(item.total_cost_amount).toFixed(2)}` : '-',
          isReturn: item.is_return ? '是' : '否'
        };
      });

      return {
        items: formattedList,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      };

    } catch (error) {
      console.error('根据订单ID获取回复历史记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取回复历史记录
   * @param {string|number} id - 售后申请ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} - 回复历史记录列表
   */
  async getReplyHistory(id, options = {}) {
    try {
      const { page = 1, pageSize = 10 } = options;
      const skip = (page - 1) * pageSize;

      // 首先验证售后申请是否存在
      const afterSalesApplication = await this.prisma.afterSalesApplication.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!afterSalesApplication) {
        throw new Error('售后申请不存在');
      }

      // 获取回复历史记录总数
      const total = await this.prisma.after_sales_reply_history.count({
        where: {
          after_sales_id: BigInt(id),
          deleted_at: null
        }
      });

      // 获取回复历史记录列表
      const replyHistoryList = await this.prisma.after_sales_reply_history.findMany({
        where: {
          after_sales_id: BigInt(id),
          deleted_at: null
        },
        include: {
          replier: {
            select: {
              id: true,
              username: true
            }
          }
        },
        orderBy: {
          created_at: 'desc' // 按创建时间倒序排列，最新的在前面
        },
        skip,
        take: pageSize
      });

      // 格式化回复历史数据
      const formattedList = replyHistoryList.map(item => {
        const formattedItem = this.formatReplyHistoryData(item);

        // 添加一些额外的显示字段
        return {
          ...formattedItem,
          modifyTime: this.formatDateTime(item.created_at),
          modifier: item.replier_name || '系统',
          companyAmount: item.company_cost_amount ? `${parseFloat(item.company_cost_amount).toFixed(2)}` : '-',
          supplierAmount: item.supplier_cost_amount ? `${parseFloat(item.supplier_cost_amount).toFixed(2)}` : '-',
          supplierAfterSalesAmount: item.supplier_cost_amount ? `${parseFloat(item.supplier_cost_amount).toFixed(2)}` : '-',
          supplierCompensation: this.getCostTypeText(item.supplier_cost_type) || '-',
          totalAmount: item.total_cost_amount ? `${parseFloat(item.total_cost_amount).toFixed(2)}` : '-',
          isReturn: item.is_return ? '是' : '否'
        };
      });

      return {
        items: formattedList,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      };

    } catch (error) {
      console.error('获取回复历史记录失败:', error);
      throw error;
    }
  }

  /**
   * 格式化日期时间
   * @param {Date} date - 日期对象
   * @returns {string} - 格式化后的日期时间字符串
   */
  formatDateTime(date) {
    if (!date) return '';

    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 获取责任归属文字
   * @param {number} value - 责任归属数字值
   * @returns {string} - 责任归属文字
   */
  getResponsibilityText(value) {
    const mapping = {
      6: '客户问题',
      1: '供应商问题',
      2: '物流问题',
      3: '销售员问题',
      4: '采购问题',
      5: '产品问题'
    };
    return mapping[value] || value;
  }

  /**
   * 获取费用类型文字
   * @param {number} value - 费用类型数字值
   * @returns {string} - 费用类型文字
   */
  getCostTypeText(value) {
    const mapping = {
      0: '快递费',
      1: '安装费',
      2: '其他费用'
    };
    return mapping[value] || value;
  }

  /**
   * 格式化回复历史数据
   * @param {Object} data - 原始回复历史数据
   * @returns {Object} - 格式化后的数据
   */
  formatReplyHistoryData(data) {
    return {
      id: data.id.toString(),
      afterSalesId: data.after_sales_id.toString(),
      replyContent: data.reply_content,
      replyType: data.reply_type,
      replierId: data.replier_id ? data.replier_id.toString() : null,
      replierName: data.replier?.username || '系统',
      replierRole: data.replier_role,

      // 责任归属和费用信息
      responsibility: data.responsibility,
      responsibilityText: this.getResponsibilityText(data.responsibility),
      companyCostType: data.company_cost_type,
      companyCostTypeText: this.getCostTypeText(data.company_cost_type),
      companyCostAmount: data.company_cost_amount ? parseFloat(data.company_cost_amount) : null,
      supplierCostType: data.supplier_cost_type,
      supplierCostTypeText: this.getCostTypeText(data.supplier_cost_type),
      supplierCostAmount: data.supplier_cost_amount ? parseFloat(data.supplier_cost_amount) : null,
      totalCostAmount: data.total_cost_amount ? parseFloat(data.total_cost_amount) : null,
      isReturn: data.is_return,
      replyAttachments: data.reply_attachments ? JSON.parse(data.reply_attachments) : [],

      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  }

  /**
   * 格式化售后申请数据
   * @param {Object} data - 原始数据
   * @returns {Object} - 格式化后的数据
   */
  formatAfterSalesApplicationData(data) {
    return {
      id: data.id.toString(),
      afterSalesNumber: data.afterSalesNumber || data.after_sales_number,
      orderNumber: data.originalOrderNumber || data.original_order_number,
      purchaseOrderNumber: data.purchaseOrderNumber || data.purchase_order_number,
      afterSalesType: data.afterSalesType || data.after_sales_type,
      afterSalesContent: data.afterSalesContent || data.after_sales_content,
      customerDemand: data.customerDemand || data.customer_demand,

      // 人员信息 (通过关联查询获取)
      applicantName: data.applicant?.username || data.applicantName || data.applicant_name,
      purchaserName: data.purchaser?.username || data.purchaserName || data.purchaser_name,
      purchaserId: data.purchaserId || (data.purchaser_id ? data.purchaser_id.toString() : null),
      afterSalesStaffName: data.after_sales_staff?.username || data.afterSalesStaffName || data.after_sales_staff_name,
      followerName: data.follower?.username || data.followerName || data.follower_name,
      salesStaffName: data.applicant?.username || data.salesStaffName || data.sales_staff_name,
      salesDepartment: data.applicant?.dept?.name || data.salesDepartment || data.sales_department,

      // 订单信息
      originalOrderId: data.originalOrderId || (data.original_order_id ? data.original_order_id.toString() : null),
      purchaseOrderId: data.purchaseOrderId || (data.purchase_order_id ? data.purchase_order_id.toString() : null),
      orderSource: data.orderSource || data.order_source,
      orderType: data.orderType || data.order_type,
      buyerAccount: data.buyerAccount || data.buyer_account,
      deliveryAddress: data.deliveryAddress || data.delivery_address,

      // 供应商信息
      supplierId: data.supplierId || (data.supplier_id ? data.supplier_id.toString() : null),
      supplierName: data.supplierName || data.supplier_name,
      isOwnBrand: data.isOwnBrand || data.is_own_brand,

      // 状态信息
      afterSalesStatus: data.afterSalesStatus || data.after_sales_status,
      afterSalesProgress: data.afterSalesProgress || data.after_sales_progress,
      businessReturn: data.businessReturn || data.business_return,
      customerServiceReturn: data.customerServiceReturn || data.customer_service_return,
      returnAddress: data.returnAddress || data.return_address,
      returnNumber: data.returnNumber || data.return_number,

      // 回复信息
      replyContent: data.replyContent || data.reply_content,
      replyTime: data.replyTime || data.reply_time,
      replierName: data.replierName || data.replier_name,

      // 其他信息
      responsibility: data.reply_history && data.reply_history.length > 0 ? data.reply_history[0].responsibility : null,
      responsibilityText: this.getResponsibilityText(data.reply_history && data.reply_history.length > 0 ? data.reply_history[0].responsibility : null),
      erpStatus: data.erpStatus || data.erp_status || 0,
      remark: data.remark,

      // 从回复历史中获取 is_return 字段（取最新的一条记录）
      isReturn: data.reply_history && data.reply_history.length > 0 ? data.reply_history[0].is_return : null,

      // 关联数据
      products: (data.productItems || data.product_items) ? (data.productItems || data.product_items).map(item => ({
        id: item.id.toString(),
        goodsSpuId: item.goods_spu_id ? item.goods_spu_id.toString() : null,
        productName: item.productName || item.product_name,
        productCode: item.productCode || item.product_code,
        skuCode: item.skuCode || item.sku_code,
        specification: item.specification,
        productImage: item.productImage || item.product_image,
        unitPrice: item.unitPrice || item.unit_price,
        salePrice: item.salePrice || item.sale_price,
        costPrice: item.costPrice || item.cost_price,
        quantity: item.quantity,
        isReturn: item.isReturn || item.is_return,
        returnQuantity: item.returnQuantity || item.return_quantity,
        // 供应商信息
        suggestedSupplierId: item.suggestedSupplierId || (item.suggested_supplier_id ? item.suggested_supplier_id.toString() : null),
        suggestedSupplierName: item.suggestedSupplierName || item.suggested_supplier_name,
        actualSupplierId: item.actualSupplierId || (item.actual_supplier_id ? item.actual_supplier_id.toString() : null),
        actualSupplierName: item.actualSupplierName || item.actual_supplier_name,
        // 物流信息
        shippingStatus: item.shippingStatus || item.shipping_status || 0,
        shippingNumber: item.shippingNumber || item.shipping_number,
        shippingInfo: item.shippingInfo || item.shipping_info
      })) : [],

      attachments: data.attachments ? data.attachments.map(attachment => ({
        id: attachment.id.toString(),
        attachmentType: attachment.attachmentType || attachment.attachment_type,
        fileName: attachment.fileName || attachment.file_name,
        fileUrl: attachment.fileUrl || attachment.file_url,
        fileSize: attachment.fileSize || attachment.file_size,
        fileType: attachment.fileType || attachment.file_type,
        uploaderName: attachment.uploaderName || attachment.uploader_name,
        createdAt: attachment.createdAt || attachment.created_at
      })) : [],

      replyHistory: (data.replyHistory || data.reply_history) ? (data.replyHistory || data.reply_history).map(reply => ({
        id: reply.id.toString(),
        replyContent: reply.replyContent || reply.reply_content,
        replyType: reply.replyType || reply.reply_type,
        replierName: reply.replierName || reply.replier_name,
        replierRole: reply.replierRole || reply.replier_role,
        createdAt: reply.createdAt || reply.created_at
      })) : [],

      logs: data.logs ? data.logs.map(log => ({
        id: log.id.toString(),
        operationType: log.operationType || log.operation_type,
        operationContent: log.operationContent || log.operation_content,
        oldValue: log.oldValue || log.old_value,
        newValue: log.newValue || log.new_value,
        operatorName: log.operatorName || log.operator_name,
        operatorRole: log.operatorRole || log.operator_role,
        createdAt: log.createdAt || log.created_at
      })) : [],

      // 时间信息
      createdAt: data.createdAt || data.created_at,
      updatedAt: data.updatedAt || data.updated_at
    };
  }
}

module.exports = AfterSalesService;
