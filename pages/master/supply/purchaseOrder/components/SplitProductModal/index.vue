<template>
  <a-drawer
    v-model:visible="visible"
    title=""
    width="70%"
    :footer="false"
    :header="false"
    @cancel="handleCancel"
    class="split-product-drawer"
  >
    <div class="split-drawer-content">
      <!-- 顶部操作按钮 -->
      <div class="drawer-header">
        <div class="header-buttons">
          <a-button @click="handleBack" size="large" class="return-btn">
            返回
          </a-button>
          <a-button
            v-if="!showSplitConfig"
            type="primary"
            @click="showSplitConfiguration"
            size="large"
            class="split-btn"
            :disabled="!canSplitProducts"
          >
            拆分商品
          </a-button>
          <a-button
            v-if="showSplitConfig"
            type="primary"
            @click="addSplitItem"
            size="large"
            class="add-btn"
            :disabled="!hasAvailableQuantity"
          >
            新增
          </a-button>
        </div>
      </div>

      <!-- 商品信息表格 -->
      <div class="split-table-container" v-if="!showSplitConfig">
        <a-table
          :data="groupedTableData"
          :pagination="false"
          :bordered="true"
          class="split-product-table"
          :scroll="{ x: 1400 }"
        >
          <template #columns>
            <a-table-column
              title="拆分号"
              align="center"
              width="180"
              fixed="left"
            >
              <template #cell="{ record }">
                <a-tag color="blue">{{ record.splitNumber }}</a-tag>
              </template>
            </a-table-column>

            <a-table-column title="商品信息" width="350">
              <template #cell="{ record }">
                <div class="products-info-cell">
                  <div
                    v-for="(item, index) in record.items"
                    :key="item.id"
                    class="product-item"
                  >
                    <div class="product-info">
                      <div class="product-image">
                        <img
                          :src="item.productImage || '/placeholder-product.png'"
                          alt="商品图片"
                        />
                      </div>
                      <div class="product-details">
                        <div class="product-name">{{ item.productName }}</div>
                        <div class="product-code">
                          商品编号: {{ item.productCode }}
                        </div>
                        <div class="product-sku">配货编号: {{ item.sku }}</div>
                        <div class="product-spec">
                          规格型号: {{ item.specification }}
                        </div>
                      </div>
                    </div>
                    <div
                      v-if="index < record.items.length - 1"
                      class="product-divider"
                    ></div>
                  </div>
                </div>
              </template>
            </a-table-column>

            <a-table-column title="数量" align="center" width="150">
              <template #cell="{ record }">
                <div class="quantity-info">
                  <div
                    v-for="(item, index) in record.items"
                    :key="item.id"
                    class="quantity-item"
                  >
                    <div class="quantity-detail">
                      <div class="unit-price">单价: ¥{{ item.unitPrice }}</div>
                      <div class="quantity">数量: {{ item.quantity }} 件</div>
                    </div>
                    <div
                      v-if="index < record.items.length - 1"
                      class="quantity-divider"
                    ></div>
                  </div>
                </div>
              </template>
            </a-table-column>

            <a-table-column title="供货价" align="center" width="120">
              <template #cell="{ record }">
                <div class="supply-price-info">
                  <div
                    v-for="(item, index) in record.items"
                    :key="item.id"
                    class="supply-price-item"
                  >
                    <div class="supply-price">
                      ¥{{ (item.unitPrice * item.quantity).toFixed(2) }}
                    </div>
                    <div
                      v-if="index < record.items.length - 1"
                      class="price-divider"
                    ></div>
                  </div>
                </div>
              </template>
            </a-table-column>

            <a-table-column title="更新时间" align="center" width="180">
              <template #cell="{ record }">
                <span class="time-text">{{
                  formatDateTime(record.updateTime)
                }}</span>
              </template>
            </a-table-column>

            <a-table-column title="发货信息" align="center" width="200">
              <template #cell="{ record }">
                <div class="shipping-info">
                  <div class="shipping-method">配送方式: 快递配送</div>
                  <div class="shipping-contact">
                    送货联系人: {{ record.contactPerson || "-" }}
                  </div>
                  <div class="shipping-phone">
                    联系电话: {{ record.contactPhone || "-" }}
                  </div>
                  <div class="shipping-number" v-if="record.shippingNumber">
                    快递单号: {{ record.shippingNumber }}
                  </div>
                </div>
              </template>
            </a-table-column>

            <a-table-column title="备注" align="center" width="150">
              <template #cell="{ record }">
                <div class="remark-cell">
                  <a-textarea
                    v-if="record.isEditable"
                    v-model="record.remark"
                    placeholder="请输入备注"
                    :auto-size="{ minRows: 2, maxRows: 4 }"
                    size="small"
                  />
                  <span v-else class="remark-text">{{
                    record.remark || "-"
                  }}</span>
                </div>
              </template>
            </a-table-column>

            <a-table-column
              title="操作"
              align="center"
              width="120"
              fixed="right"
            >
              <template #cell="{ record, rowIndex }">
                <div class="action-buttons">
                  <a-button
                    type="primary"
                    size="small"
                    class="action-btn view-btn"
                  >
                    查看详情
                  </a-button>
                  <a-button
                    type="primary"
                    size="small"
                    class="action-btn edit-btn"
                  >
                    更新信息
                  </a-button>
                  <a-button
                    type="primary"
                    size="small"
                    class="action-btn qr-btn"
                  >
                    复制二维码
                  </a-button>
                </div>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>

      <!-- 拆分配置界面 -->
      <div class="split-config-container" v-if="showSplitConfig">
        <!-- 拆分配置表格 -->
        <div class="split-config-table">
          <a-table
            :data="productListForSplit"
            :pagination="false"
            :bordered="true"
            class="config-table"
            :scroll="{ x: 'max-content' }"
          >
            <template #columns>
              <a-table-column title="商品信息" width="400" fixed="left">
                <template #cell="{ record }">
                  <div class="product-info-cell">
                    <div class="product-image">
                      <img
                        :src="record.productImage || '/placeholder-product.png'"
                        alt="商品图片"
                      />
                    </div>
                    <div class="product-details">
                      <div class="product-name">{{ record.productName }}</div>
                      <div class="product-code">
                        商品编号: {{ record.productCode }}
                      </div>
                      <div class="product-sku">配货编号: {{ record.sku }}</div>
                      <div class="product-spec">
                        规格型号: {{ record.specification }}
                      </div>
                    </div>
                  </div>
                </template>
              </a-table-column>

              <a-table-column title="单价" align="center" width="120">
                <template #cell="{ record }">
                  <span class="price-text">¥{{ record.unitPrice }}</span>
                </template>
              </a-table-column>

              <a-table-column title="总数量" align="center" width="120">
                <template #cell="{ record }">
                  <span class="quantity-text">{{ record.quantity || 0 }}</span>
                </template>
              </a-table-column>

              <a-table-column title="待分配数量" align="center" width="120">
                <template #cell="{ record, rowIndex }">
                  <span class="quantity-text pending">{{
                    getRemainingQuantity(rowIndex)
                  }}</span>
                </template>
              </a-table-column>

              <!-- 动态生成分配数量列 -->
              <a-table-column
                v-for="(splitGroup, index) in splitGroups"
                :key="`split-${index}`"
                align="center"
                width="180"
              >
                <template #title>
                  <div class="split-column-header">
                    <span>分配数量</span>
                    <!-- 只有非第一列才显示删除按钮 -->
                    <a-button
                      v-if="index > 0"
                      type="text"
                      size="mini"
                      @click="removeSplitColumn(index)"
                      class="delete-column-btn"
                    >
                      <icon-delete />
                    </a-button>
                  </div>
                </template>
                <template #cell="{ record, rowIndex }">
                  <a-input-number
                    v-model="record.splitQuantities[index]"
                    :min="0"
                    :max="record.quantity"
                    placeholder="数量"
                    size="small"
                    style="width: 100%"
                    @change="handleQuantityChange(rowIndex, index)"
                  />
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>

        <!-- 提示信息 -->
        <div class="split-notice">
          <span class="notice-text"
            >注：商品一旦确认拆分无法还原，请谨慎拆分；可先拆部分，剩余确定后再拆</span
          >
        </div>

        <!-- 底部操作按钮 -->
        <div class="config-footer">
          <a-button @click="cancelSplitConfig" size="large">取消</a-button>
          <a-button
            type="primary"
            @click="handleSubmit"
            size="large"
            :disabled="!canSubmit"
          >
            保存
          </a-button>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconDelete } from "@arco-design/web-vue/es/icon";
import { purchaseOrderSplitApi } from "@/api/master/csm/purchaseOrderSplit.js";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  productData: {
    type: Object,
    default: null,
  },
  orderData: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(["update:modelValue", "submit"]);

const visible = ref(props.modelValue);

// 原始商品数据
const originalProduct = ref(null);
const selectedProduct = ref(null);

// 表格数据 - 显示已拆分的商品
const tableData = ref([]);

// 视图状态控制
const showSplitConfig = ref(false);

// 拆分组数据 - 每个组代表一个拆分目标，默认只有一个分配列
const splitGroups = ref([{ id: 1, name: "分配1" }]);

// 当前订单的所有商品列表（用于拆分配置）
const productListForSplit = ref([]);

// 拆分配置数据（已废弃，使用新的数据结构）
const splitConfigData = ref([{ quantity: 1, id: 1 }]);

// 订单基础信息
const orderInfo = ref({
  orderSource: "",
  recipientName: "",
  deliveryAddress: "",
  recipientPhone: "",
});

// 订单统计信息
const orderStats = ref({
  totalQuantity: 0,
  totalSplitQuantity: 0,
  totalAvailableSplitQuantity: 0,
});

// 分组后的表格数据 - 同一个拆分订单的商品合并为一行
const groupedTableData = computed(() => {
  if (!tableData.value || tableData.value.length === 0) return [];

  // 按拆分号分组，每个拆分单只显示一行
  const grouped = {};
  tableData.value.forEach((item) => {
    const key = item.splitNumber;
    if (!grouped[key]) {
      // 创建拆分单记录，包含所有商品信息
      grouped[key] = {
        id: item.splitId,
        splitNumber: item.splitNumber,
        updateTime: item.updateTime,
        shippingStatus: item.shippingStatus,
        shippingNumber: item.shippingNumber,
        contactPerson: item.contactPerson || "待分配",
        contactPhone: item.contactPhone || "待分配",
        remark: item.remark,
        isOriginal: false,
        isEditable: false,
        totalQuantity: item.quantity,
        items: [item],
      };
    } else {
      grouped[key].totalQuantity += item.quantity;
      grouped[key].items.push(item);
      // 更新状态为最新的状态
      if (item.updateTime > grouped[key].updateTime) {
        grouped[key].updateTime = item.updateTime;
        grouped[key].shippingStatus = item.shippingStatus;
        grouped[key].shippingNumber = item.shippingNumber;
      }
    }
  });

  return Object.values(grouped);
});

// 计算是否有可分配数量
const hasAvailableQuantity = computed(() => {
  if (!showSplitConfig.value) return true;

  // 检查是否有商品的待分配数量大于0
  return productListForSplit.value.some((_, index) => {
    return getRemainingQuantity(index) > 0;
  });
});

// 计算是否可以拆分商品
const canSplitProducts = computed(() => {
  // 计算 groupedTableData 的所有 quantity 数量总和
  const totalSplitQuantity = groupedTableData.value.reduce((sum, record) => {
    return sum + record.totalQuantity;
  }, 0);

  // 检查总拆分数量是否等于订单总数量
  return totalSplitQuantity < orderStats.value.totalQuantity;
});

// 计算是否可以提交
const canSubmit = computed(() => {
  if (!showSplitConfig.value) return true;

  // 检查是否有商品分配了数量，且所有分配数量都合理
  const hasAllocated = productListForSplit.value.some((product) => {
    const totalAllocated = product.splitQuantities.reduce(
      (sum, qty) => sum + (qty || 0),
      0
    );
    return totalAllocated > 0;
  });

  const allValid = productListForSplit.value.every((product) => {
    const totalAllocated = product.splitQuantities.reduce(
      (sum, qty) => sum + (qty || 0),
      0
    );
    return totalAllocated <= (product.availableSplitQuantity || 0);
  });

  return hasAllocated && allValid;
});

// 计算总拆分数量（已废弃）
const totalSplitQuantity = computed(() => {
  if (showSplitConfig.value) {
    return splitConfigData.value.reduce(
      (sum, item) => sum + (item.quantity || 0),
      0
    );
  }
  return tableData.value
    .filter((item) => !item.isOriginal)
    .reduce((sum, item) => sum + (item.quantity || 0), 0);
});

// 计算剩余数量（已废弃，使用getRemainingQuantity方法）
const remainingQuantity = computed(() => {
  if (!originalProduct.value) return 0;
  return originalProduct.value.quantity - totalSplitQuantity.value;
});

// 获取指定商品的剩余数量
const getRemainingQuantity = (productIndex) => {
  if (!productListForSplit.value[productIndex]) return 0;

  const product = productListForSplit.value[productIndex];
  const totalAllocated = product.splitQuantities.reduce(
    (sum, qty) => sum + (qty || 0),
    0
  );
  return product.availableSplitQuantity - totalAllocated;
};

// 获取发货状态文本
const getShippingStatusText = (status) => {
  const statusMap = {
    0: "待发货",
    1: "发货未完成",
    2: "待收货",
    3: "已收货",
  };
  return statusMap[status] || "待发货";
};

// 格式化时间为 YYYY-MM-dd HH:mm:ss
const formatDateTime = (dateTime) => {
  if (!dateTime) return "-";

  try {
    const date = new Date(dateTime);
    if (isNaN(date.getTime())) return "-";

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error("时间格式化失败:", error);
    return "-";
  }
};

// 加载已拆分的商品数据（用于商品信息表格）
const loadSplitProductsData = async () => {
  try {
    if (!props.orderData?.id) return;

    const response = await purchaseOrderSplitApi.getPurchaseOrderSplits(
      props.orderData.id
    );
    if (response.code === 200 && response.data?.items) {
      // 将拆分数据转换为表格显示格式
      const splitItems = [];

      response.data.items.forEach((split) => {
        split.splitItems?.forEach((item) => {
          splitItems.push({
            id: item.id,
            productName: item.productName,
            productCode: item.productCode,
            sku: item.sku,
            specification: item.specification,
            productImage: item.productImage,
            quantity: item.splitQuantity,
            unitPrice: item.unitPrice,
            updateTime: item.updatedAt || split.splitTime,
            shippingStatus: getShippingStatusText(item.shippingStatus),
            shippingNumber: item.shippingNumber,
            contactPerson: split.contactPerson || "待分配",
            contactPhone: split.contactPhone || "待分配",
            remark: item.splitRemark || "",
            isOriginal: false,
            isEditable: false,
            splitNumber: split.splitNumber,
            splitGroupName: `拆分单${split.splitNumber}`,
            splitId: split.id,
          });
        });
      });

      // 商品信息表格只显示已拆分的商品
      tableData.value = splitItems;
    }
  } catch (error) {
    console.error("加载拆分数据失败:", error);
    Message.error("加载拆分数据失败");
  }
};

// 加载订单商品数据（用于拆分配置表格）
const loadOrderItemsData = async () => {
  try {
    if (!props.orderData?.id) return;

    const response = await purchaseOrderSplitApi.getPurchaseOrderItems(
      props.orderData.id
    );
    if (response.code === 200 && response.data?.order) {
      const orderData = response.data.order;

      // 更新订单基础信息
      orderInfo.value = {
        orderSource: orderData.orderSource || "商城",
        recipientName: orderData.recipientName || "-",
        deliveryAddress: orderData.recipientAddress || "-",
        recipientPhone: orderData.recipientPhone || "-",
      };

      // 更新订单统计信息
      if (response.data?.stats) {
        orderStats.value = {
          totalQuantity: response.data.stats.totalQuantity || 0,
          totalSplitQuantity: response.data.stats.totalSplitQuantity || 0,
          totalAvailableSplitQuantity:
            response.data.stats.totalAvailableSplitQuantity || 0,
        };
      }

      // 初始化拆分配置表格数据（显示所有商品及其可分配数量）
      productListForSplit.value = orderData.items.map((item) => ({
        id: item.id,
        productName: item.productName,
        productCode: item.productCode,
        sku: item.sku,
        specification: item.specification,
        productImage: item.productImage,
        unitPrice: item.unitPrice,
        quantity: item.quantity, // 总数量
        splitQuantity: item.splitQuantity, // 已拆分数量
        availableSplitQuantity: item.availableSplitQuantity, // 可拆分数量
        canSplit: item.canSplit,
        splitProgress: item.splitProgress,
        splitQuantities: new Array(splitGroups.value.length).fill(0), // 当前拆分配置
      }));
    }
  } catch (error) {
    console.error("加载订单商品数据失败:", error);
    Message.error("加载订单商品数据失败");
  }
};

watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal;
    if (newVal && props.orderData?.id) {
      // 当打开模态框时，并行加载拆分数据和商品数据
      Promise.all([loadSplitProductsData(), loadOrderItemsData()]);
    } else if (newVal && props.productData) {
      initializeData();
    }
  }
);

watch(visible, (newVal) => {
  emit("update:modelValue", newVal);
});

// 初始化数据
const initializeData = () => {
  // 重置视图状态
  showSplitConfig.value = false;

  if (props.productData) {
    // 单个商品拆分模式
    originalProduct.value = { ...props.productData };
    selectedProduct.value = { ...props.productData };

    // 初始化表格数据，显示原始商品和已拆分的商品
    tableData.value = [
      {
        ...props.productData,
        isOriginal: true,
        isEditable: false,
        updateTime: "2025-06-24 15:31:34",
        shippingStatus: "待发货",
        shippingNumber: "",
        remark: "",
      },
    ];

    // 初始化单个商品的拆分配置
    productListForSplit.value = [
      {
        ...props.productData,
        splitQuantities: new Array(splitGroups.value.length).fill(0),
      },
    ];
  } else if (props.orderData && props.orderData.products) {
    // 订单商品拆分模式
    originalProduct.value = null;

    // 显示订单中已拆分的商品记录
    tableData.value = props.orderData.splitProducts || [];

    // 初始化订单所有商品的拆分配置
    productListForSplit.value = props.orderData.products.map((product) => ({
      ...product,
      splitQuantities: new Array(splitGroups.value.length).fill(0),
    }));
  }

  // 初始化拆分配置数据（保持兼容性）
  const defaultQuantity = Math.floor((props.productData?.quantity || 2) / 2);
  splitConfigData.value = [
    { quantity: defaultQuantity, id: 1 },
    { quantity: (props.productData?.quantity || 2) - defaultQuantity, id: 2 },
  ];
};

// 数量变化处理
const handleQuantityChange = (productIndex, groupIndex) => {
  if (productIndex !== undefined && groupIndex !== undefined) {
    // 确保数量不超过商品可分配数量
    const product = productListForSplit.value[productIndex];
    if (product) {
      const currentValue = product.splitQuantities[groupIndex] || 0;
      const totalOtherAllocated = product.splitQuantities.reduce(
        (sum, qty, index) => {
          return index === groupIndex ? sum : sum + (qty || 0);
        },
        0
      );

      if (currentValue + totalOtherAllocated > product.availableSplitQuantity) {
        product.splitQuantities[groupIndex] =
          product.availableSplitQuantity - totalOtherAllocated;
        Message.warning(
          `分配数量不能超过可分配数量${product.availableSplitQuantity}`
        );
      }
    }
  }
};

// 显示拆分配置界面
const showSplitConfiguration = () => {
  showSplitConfig.value = true;
};

// 添加拆分项（新增分配列）
const addSplitItem = () => {
  if (showSplitConfig.value) {
    // 在拆分配置模式下添加新的分配列
    const newGroupId = splitGroups.value.length + 1;
    splitGroups.value.push({
      id: newGroupId,
      name: `分配${newGroupId}`,
    });

    // 为所有商品添加新的分配数量字段
    productListForSplit.value.forEach((product) => {
      product.splitQuantities.push(0);
    });

    Message.success(`已添加分配数量${newGroupId}列`);
  } else {
    // 在列表模式下添加新的商品项
    if (!originalProduct.value) return;

    const newItem = {
      ...originalProduct.value,
      id: `split_${Date.now()}`,
      quantity: 1,
      isOriginal: false,
      isEditable: true,
      updateTime: new Date().toISOString().slice(0, 19).replace("T", " "),
      shippingStatus: "待发货",
      shippingNumber: "",
      remark: `拆分${tableData.value.length}`,
    };

    tableData.value.push(newItem);
  }
};

// 删除拆分项
const removeSplitItem = (index) => {
  if (tableData.value[index] && !tableData.value[index].isOriginal) {
    tableData.value.splice(index, 1);
  }
};

// 删除拆分配置项
const removeSplitConfigItem = (index) => {
  if (splitConfigData.value.length > 1) {
    splitConfigData.value.splice(index, 1);
  }
};

// 删除拆分列
const removeSplitColumn = (columnIndex) => {
  if (columnIndex === 0) {
    Message.warning("第一列不能删除");
    return;
  }

  if (splitGroups.value.length <= 1) {
    Message.warning("至少需要保留一个分配列");
    return;
  }

  // 删除拆分组
  splitGroups.value.splice(columnIndex, 1);

  // 删除所有商品对应的分配数量
  productListForSplit.value.forEach((product) => {
    product.splitQuantities.splice(columnIndex, 1);
  });

  // 重新编号剩余的分配组
  splitGroups.value.forEach((group, index) => {
    group.id = index + 1;
    group.name = `分配${index + 1}`;
  });

  Message.success(`已删除分配数量${columnIndex + 1}列`);
};

// 取消拆分配置
const cancelSplitConfig = () => {
  showSplitConfig.value = false;
};

// 返回处理
const handleBack = () => {
  if (showSplitConfig.value) {
    showSplitConfig.value = false;
  } else {
    visible.value = false;
  }
};

// 提交拆分
const handleSubmit = async () => {
  try {
    if (showSplitConfig.value) {
      // 拆分配置模式 - 处理多商品多分配的情况
      const splitResults = [];

      for (const product of productListForSplit.value) {
        const totalAllocated = product.splitQuantities.reduce(
          (sum, qty) => sum + (qty || 0),
          0
        );

        if (totalAllocated === 0) {
          continue; // 跳过没有分配的商品
        }

        if (totalAllocated > product.availableSplitQuantity) {
          Message.error(
            `商品"${product.productName}"的分配数量超过了可分配数量`
          );
          return;
        }

        const splitItems = [];

        // 为每个分配组创建拆分项
        product.splitQuantities.forEach((quantity, index) => {
          if (quantity > 0) {
            splitItems.push({
              quantity: quantity,
              splitIndex: index,
              splitGroupName:
                splitGroups.value[index]?.name || `分配${index + 1}`,
              remark: `${
                splitGroups.value[index]?.name || `分配${index + 1}`
              } - 数量${quantity}`,
            });
          }
        });

        if (splitItems.length > 0) {
          splitResults.push({
            originalProduct: {
              id: product.id,
              productName: product.productName,
              productCode: product.productCode,
              sku: product.sku,
              specification: product.specification,
              unitPrice: product.unitPrice,
              quantity: product.quantity,
            },
            splitItems: splitItems,
            totalAllocated: totalAllocated,
            remainingQuantity: product.availableSplitQuantity - totalAllocated,
          });
        }
      }

      if (splitResults.length === 0) {
        Message.warning("请至少为一个商品分配数量");
        return;
      }

      const splitData = {
        splitResults: splitResults,
      };

      console.log("提交拆分数据:", splitData);

      // 调用拆分接口
      if (props.orderData?.id) {
        const response = await purchaseOrderSplitApi.splitProducts(
          props.orderData.id,
          { splitData }
        );
        if (response.code === 200) {
          Message.success("商品拆分成功");
          emit("submit", splitData);
          visible.value = false;

          // 重新加载数据
          await Promise.all([loadSplitProductsData(), loadOrderItemsData()]);
        } else {
          Message.error(response.message || "拆分失败");
        }
      } else {
        Message.error("订单信息不完整");
      }
    } else {
      // 列表模式 - 原有逻辑保持不变
      if (!originalProduct.value) {
        Message.error("没有可拆分的商品数据");
        return;
      }

      const splitItems = tableData.value.filter((item) => !item.isOriginal);
      if (splitItems.length === 0) {
        Message.error("请至少添加一个拆分项");
        return;
      }

      const splitData = {
        type: "single-product-split",
        originalProduct: originalProduct.value,
        splitItems: splitItems,
        splitAt: new Date().toISOString(),
      };

      emit("submit", splitData);
      Message.success("商品拆分成功");
      visible.value = false;
    }
  } catch (error) {
    console.error("拆分提交失败:", error);
    Message.error("拆分提交失败");
  }
};

// 取消操作
const handleCancel = () => {
  showSplitConfig.value = false;
  visible.value = false;
};
</script>

<style scoped>
.split-product-drawer {
  background: #f5f5f5;
}

.split-product-drawer :deep(.arco-drawer-body) {
  padding: 0;
  background: #f5f5f5;
}

.split-drawer-content {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.drawer-header {
  background: #ffffff;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e6eb;
  flex-shrink: 0;
}

.header-buttons {
  display: flex;
  gap: 12px;
}

.return-btn {
  background: #f2f3f5;
  border: 1px solid #d9d9d9;
  color: #1d2129;
}

.return-btn:hover {
  background: #e5e6eb;
  border-color: #c9cdd4;
}

.split-btn {
  background: #ff4757;
  border-color: #ff4757;
}

.split-btn:hover {
  background: #ff3742;
  border-color: #ff3742;
}

.split-btn:disabled {
  background: #f7f8fa;
  border-color: #e5e6eb;
  color: #c9cdd4;
}

.split-table-container {
  flex: 1;
  padding: 16px 24px;
  overflow: auto;
}

.split-product-table {
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.split-product-table :deep(.arco-table-th) {
  background: #f7f8fa;
  font-weight: 500;
  color: #1d2129;
  border-bottom: 1px solid #e5e6eb;
}

.split-product-table :deep(.arco-table-td) {
  border-bottom: 1px solid #f2f3f5;
}

.split-product-table
  :deep(.arco-table-tbody .arco-table-tr:hover .arco-table-td) {
  background: #f8f9fa;
}

.product-info-cell {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.product-image {
  width: 50px;
  height: 50px;
  margin-right: 12px;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-code {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 2px;
}

.product-sku {
  font-size: 12px;
  color: #165dff;
  margin-bottom: 2px;
}

.product-spec {
  font-size: 12px;
  color: #86909c;
}

.quantity-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.unit {
  font-size: 12px;
  color: #86909c;
}

.price-text {
  font-size: 14px;
  font-weight: 500;
  color: #f53f3f;
}

.shipping-info {
  text-align: center;
}

.shipping-status {
  font-size: 12px;
  color: #1d2129;
  margin-bottom: 2px;
}

.shipping-number {
  font-size: 11px;
  color: #86909c;
}

.remark-cell {
  width: 100%;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.action-btn {
  font-size: 12px;
  padding: 2px 8px;
  height: 24px;
  border-radius: 4px;
}

.edit-btn {
  background: #ff4757;
  border-color: #ff4757;
  color: #ffffff;
}

.edit-btn:hover {
  background: #ff3742;
  border-color: #ff3742;
}

.view-btn {
  background: #52c41a;
  border-color: #52c41a;
  color: #ffffff;
}

.view-btn:hover {
  background: #389e0d;
  border-color: #389e0d;
}

.split-btn {
  background: #1890ff;
  border-color: #1890ff;
  color: #ffffff;
}

.split-btn:hover {
  background: #096dd9;
  border-color: #096dd9;
}

.qr-btn {
  background: #722ed1;
  border-color: #722ed1;
  color: #ffffff;
}

.qr-btn:hover {
  background: #531dab;
  border-color: #531dab;
}

.add-btn {
  background: #52c41a;
  border-color: #52c41a;
}

.add-btn:hover {
  background: #389e0d;
  border-color: #389e0d;
}

/* 拆分配置界面样式 */
.split-config-container {
  flex: 1;
  padding: 16px 24px;
  overflow: auto;
}

.product-info-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.quantity-summary {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  margin-bottom: 24px;
}

.summary-item {
  text-align: center;
}

.summary-item .label {
  display: block;
  font-size: 14px;
  color: #86909c;
  margin-bottom: 8px;
}

.summary-item .value {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
}

.summary-item .value.pending {
  color: #ff7d00;
}

.summary-item .value.allocated {
  color: #00b42a;
}

.split-config-table {
  margin-bottom: 16px;
}

.config-table {
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.config-table :deep(.arco-table-th) {
  background: #f7f8fa;
  font-weight: 500;
  color: #1d2129;
  border-bottom: 1px solid #e5e6eb;
}

.config-table :deep(.arco-table-td) {
  border-bottom: 1px solid #f2f3f5;
}

.split-notice {
  margin: 16px 0;
  text-align: center;
}

.notice-text {
  color: #f53f3f;
  font-size: 14px;
}

.config-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding-top: 24px;
  border-top: 1px solid #e5e6eb;
  margin-top: 24px;
}

/* 数量文本样式 */
.quantity-text {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
}

.quantity-text.pending {
  color: #ff7d00;
  font-weight: 600;
}

/* 表格滚动优化 */
.config-table :deep(.arco-table-container) {
  overflow-x: auto;
}

.config-table :deep(.arco-table-th),
.config-table :deep(.arco-table-td) {
  white-space: nowrap;
}

/* 输入框样式优化 */
.config-table :deep(.arco-input-number) {
  width: 100% !important;
}

.config-table :deep(.arco-input-number-input) {
  text-align: center;
}

/* 拆分列标题样式 */
.split-column-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.delete-column-btn {
  color: #f53f3f;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-column-btn:hover {
  background: #ffece8;
  color: #cb272d;
}

.delete-column-btn :deep(.arco-icon) {
  font-size: 12px;
}

/* 数量显示样式 */
.quantity-text {
  font-weight: 500;
  color: #1d2129;
}

/* 时间显示样式 */
.time-text {
  font-size: 12px;
  color: #86909c;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
}

/* 多商品信息样式 */
.products-info-cell {
  padding: 8px 0;
}

.product-item {
  margin-bottom: 12px;
}

.product-item:last-child {
  margin-bottom: 0;
}

.product-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.product-image {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  border-radius: 4px;
  overflow: hidden;
  background: #f2f3f5;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 4px;
  line-height: 1.4;
}

.product-code,
.product-sku,
.product-spec,
.product-quantity {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 2px;
  line-height: 1.3;
}

.product-quantity {
  color: #165dff;
  font-weight: 500;
}

.product-divider {
  height: 1px;
  background: #e5e6eb;
  margin: 8px 0;
}

/* 数量信息样式 */
.quantity-info {
  padding: 8px 0;
}

.quantity-item {
  margin-bottom: 12px;
}

.quantity-item:last-child {
  margin-bottom: 0;
}

.quantity-detail {
  text-align: center;
}

.unit-price {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 4px;
}

.quantity {
  font-size: 13px;
  color: #165dff;
  font-weight: 500;
}

.quantity-divider {
  height: 1px;
  background: #e5e6eb;
  margin: 8px 0;
}

/* 供货价样式 */
.supply-price-info {
  padding: 8px 0;
}

.supply-price-item {
  margin-bottom: 12px;
}

.supply-price-item:last-child {
  margin-bottom: 0;
}

.supply-price {
  font-size: 14px;
  color: #00b42a;
  font-weight: 500;
  text-align: center;
}

.price-divider {
  height: 1px;
  background: #e5e6eb;
  margin: 8px 0;
}

/* 发货信息样式 */
.shipping-info {
  text-align: left;
  padding: 8px;
}

.shipping-method,
.shipping-contact,
.shipping-phone,
.shipping-status,
.shipping-number {
  font-size: 12px;
  margin-bottom: 4px;
  line-height: 1.4;
}

.shipping-method {
  color: #1d2129;
  font-weight: 500;
}

.shipping-contact,
.shipping-phone {
  color: #4e5969;
}

.shipping-status {
  color: #165dff;
}

.shipping-number {
  color: #00b42a;
}

/* 备注样式 */
.remark-cell {
  padding: 8px 0;
}

.remark-text {
  font-size: 12px;
  color: #86909c;
  line-height: 1.4;
  word-break: break-word;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.action-btn {
  width: 80px;
  height: 24px;
  font-size: 12px;
  border-radius: 4px;
}

.view-btn {
  background: #165dff;
  border-color: #165dff;
}

.view-btn:hover {
  background: #4080ff;
  border-color: #4080ff;
}

.edit-btn {
  background: #00b42a;
  border-color: #00b42a;
}

.edit-btn:hover {
  background: #23c343;
  border-color: #23c343;
}

.qr-btn {
  background: #ff7d00;
  border-color: #ff7d00;
}

.qr-btn:hover {
  background: #ff9a2e;
  border-color: #ff9a2e;
}
</style>
